# YAML anchors for common configurations
x-common-resources: &common-resources
  mem_limit: 256M
  cpus: 0.25
  mem_reservation: 128M

services:
  # Infisical Secrets Management Service
  infisical:
    image: infisical/infisical:latest
    container_name: beauty-crm-infisical
    restart: unless-stopped
    depends_on:
      infisical-db:
        condition: service_healthy
      infisical-redis:
        condition: service_started
    # No ports exposed - access via Traefik only
    environment:
      # Core Configuration
      - NODE_ENV=production
      - SITE_URL=http://infisical.localhost
      - PORT=8080
      - HOST=0.0.0.0

      # Encryption & Security
      # Generated with: openssl rand -hex 32
      - ENCRYPTION_KEY=cff83b304c565ed7efdc569686aea616c0ebdb91120dea7895bafe1a59d46c6e

      # Auth Secret for JWT
      # Generated with: openssl rand -base64 32
      - AUTH_SECRET=sAihjpUroS4PyGcl4pMzJGe90gjV2BTGZNoOyt72rNU=

      # JWT Configuration
      - JWT_AUTH_SECRET=FiPnaUdr+bgGMax5HX3HRs80Ef6+edvxijCqw+uumn8=
      - JWT_REFRESH_SECRET=sAihjpUroS4PyGcl4pMzJGe90gjV2BTGZNoOyt72rNU=
      - JWT_SERVICE_SECRET=cff83b304c565ed7efdc569686aea616c0ebdb91120dea7895bafe1a59d46c6e

      # Additional Security
      - INVITE_ONLY_SIGNUP=false
      - TRUST_PROXY=true

      # Database Configuration
      - DB_CONNECTION_URI=*******************************************************************/infisical

      # Redis Configuration
      - REDIS_URL=redis://infisical-redis:6379

      # SMTP Configuration (Optional - for email notifications)
      # Disabled for now - configure when needed
      # - SMTP_HOST=smtp.gmail.com
      # - SMTP_PORT=587
      # - SMTP_SECURE=false
      # - SMTP_FROM_ADDRESS=<EMAIL>
      # - SMTP_FROM_NAME=Beauty CRM Infisical
      # - SMTP_USERNAME=
      # - SMTP_PASSWORD=

      # Telemetry (set to false for privacy)
      - TELEMETRY_ENABLED=false

      # License (for enterprise features)
      # - LICENSE_KEY=${INFISICAL_LICENSE_KEY}
    networks:
      - infisical-network
      - traefik-private
    labels:
      # Service Discovery Labels
      - "beauty-crm.service=true"
      - "beauty-crm.service.name=infisical"
      - "beauty-crm.service.port=8080"
      - "beauty-crm.service.health=/api/v1/status"
      - "beauty-crm.service.domain=infisical"
      - "beauty-crm.service.version=1.0.0"

      # Service identification
      - "app.service=infisical"
      - "app.type=infrastructure"
      - "app.environment=production"

      # Traefik configuration
      - "traefik.enable=true"
      - "traefik.docker.network=beauty_crm_traefik-private"
      - "traefik.http.routers.infisical.rule=Host(`infisical.localhost`)"
      - "traefik.http.routers.infisical.entrypoints=web"
      - "traefik.http.routers.infisical.service=infisical"
      - "traefik.http.services.infisical.loadbalancer.server.port=8080"
      - "traefik.http.services.infisical.loadbalancer.healthcheck.path=/api/v1/status"
      - "traefik.http.services.infisical.loadbalancer.healthcheck.interval=30s"
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/api/v1/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    <<: *common-resources

  # Infisical PostgreSQL Database
  infisical-db:
    image: postgres:16-alpine
    container_name: beauty-crm-infisical-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=infisical
      - POSTGRES_PASSWORD=infisical_secure_pass_2026
      - POSTGRES_DB=infisical
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    command: >
      postgres
      -c shared_buffers=64MB
      -c effective_cache_size=256MB
      -c maintenance_work_mem=32MB
      -c work_mem=8MB
      -c max_connections=50
    volumes:
      - infisical-db-data:/var/lib/postgresql/data
    networks:
      - infisical-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U infisical -d infisical"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    mem_limit: 384M
    cpus: 0.25
    mem_reservation: 192M

  # Infisical Redis Cache
  infisical-redis:
    image: redis:7-alpine
    container_name: beauty-crm-infisical-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    volumes:
      - infisical-redis-data:/data
    networks:
      - infisical-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    mem_limit: 192M
    cpus: 0.15
    mem_reservation: 96M

networks:
  infisical-network:
    name: beauty_crm_infisical-network
    driver: bridge
  traefik-private:
    external: true
    name: beauty_crm_traefik-private

volumes:
  infisical-db-data:
    driver: local
  infisical-redis-data:
    driver: local

