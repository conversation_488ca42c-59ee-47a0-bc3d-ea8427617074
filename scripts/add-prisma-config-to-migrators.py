#!/usr/bin/env python3
"""
Add prisma.config.ts copy command to all Dockerfile.migrator files
"""

import re
from pathlib import Path

# Service paths and their Dockerfile.migrator locations
SERVICES = [
    ('appointment-planner-backend', 'services/appointment/appointment-planner-backend'),
    ('appointment-management-backend', 'services/appointment/appointment-management-backend'),
    ('salon-management-backend', 'services/salon/salon-management-backend'),
    ('staff-management-backend', 'services/staff/staff-management-backend'),
    ('treatment-management-backend', 'services/treatment/treatment-management-backend'),
    ('public-identity-management-backend', 'services/public-identity/public-identity-management-backend'),
]

def update_dockerfile(service_name, service_path):
    """Add prisma.config.ts copy to Dockerfile.migrator"""
    root = Path('/private/var/www/2025/ollamar1/beauty-crm')
    dockerfile_path = root / service_path / 'Dockerfile.migrator'
    
    if not dockerfile_path.exists():
        return False, "File not found"
    
    with open(dockerfile_path, 'r') as f:
        content = f.read()
    
    # Check if prisma.config.ts is already being copied
    if 'prisma.config.ts' in content:
        return True, "Already has prisma.config.ts"
    
    # Pattern to find the COPY prisma line
    pattern = rf'(# Copy Prisma schema.*?\nCOPY {re.escape(service_path)}/prisma \./prisma/)'
    
    # Replacement with added prisma.config.ts
    replacement = rf'\1\nCOPY {service_path}/prisma.config.ts ./prisma.config.ts'
    
    # Apply the replacement
    new_content = re.sub(pattern, replacement, content)
    
    if new_content == content:
        return False, "Pattern not found"
    
    with open(dockerfile_path, 'w') as f:
        f.write(new_content)
    
    return True, "Updated"

def main():
    print("Adding prisma.config.ts to all Dockerfile.migrator files...")
    print()
    
    updated_count = 0
    skipped_count = 0
    failed_count = 0
    
    for service_name, service_path in SERVICES:
        success, message = update_dockerfile(service_name, service_path)
        
        if success:
            if "Already" in message:
                print(f"ℹ️  {service_name:45} → {message}")
                skipped_count += 1
            else:
                print(f"✅ {service_name:45} → {message}")
                updated_count += 1
        else:
            print(f"❌ {service_name:45} → {message}")
            failed_count += 1
    
    print()
    print(f"✅ Updated {updated_count} files")
    print(f"ℹ️  Skipped {skipped_count} files (already configured)")
    
    if failed_count > 0:
        print(f"⚠️  {failed_count} files failed to update")

if __name__ == '__main__':
    main()

