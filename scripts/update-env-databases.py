#!/usr/bin/env python3
"""
Update all .env files with correct database names to match prisma.config.ts
"""

from pathlib import Path

# Mapping of service paths to their database names
DATABASE_MAPPINGS = {
    'shared-platform-engineering/platform-db-client': 'beauty_crm',
    'shared-platform-engineering/neural-mcp': 'beauty_crm_neural',
    'services/salon/salon-management-backend': 'beauty_crm_salon',
    'services/staff/staff-management-backend': 'beauty_crm_staff',
    'services/treatment/treatment-management-backend': 'beauty_crm_treatment',
    'services/appointment/appointment-management-backend': 'beauty_crm_appointment',
    'services/appointment/appointment-planner-backend': 'beauty_crm_planner',
    'services/public-identity/public-identity-management-backend': 'beauty_crm_identity',
}

ENV_TEMPLATE = """# Placeholder DATABASE_URL for Prisma client generation
# Actual connection URL should be provided at runtime
DATABASE_URL="postgresql://user:password@localhost:5432/{db_name}?schema=public"
"""

def update_env_file(service_path, db_name):
    """Update .env file with correct database name"""
    root = Path('/private/var/www/2025/ollamar1/beauty-crm')
    env_path = root / service_path / '.env'
    
    content = ENV_TEMPLATE.format(db_name=db_name)
    
    with open(env_path, 'w') as f:
        f.write(content)
    
    return True

def main():
    print("Updating all .env files with correct database names...")
    print()
    
    updated_count = 0
    
    for service_path, db_name in DATABASE_MAPPINGS.items():
        service_name = service_path.split('/')[-1]
        
        if update_env_file(service_path, db_name):
            print(f"✅ {service_name:40} → {db_name}")
            updated_count += 1
    
    print()
    print(f"✅ Updated {updated_count}/{len(DATABASE_MAPPINGS)} .env files")

if __name__ == '__main__':
    main()

