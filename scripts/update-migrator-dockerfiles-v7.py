#!/usr/bin/env python3
"""
Update all Dockerfile.migrator files to use Bun 1.3.5+ for Prisma v7.2.0 compatibility
Prisma v7.2.0 requires Node.js 20.19+, 22.12+, or 24.0+
"""

import re
from pathlib import Path

# Find all Dockerfile.migrator files
MIGRATOR_FILES = [
    'services/appointment/appointment-planner-backend/Dockerfile.migrator',
    'services/appointment/appointment-management-backend/Dockerfile.migrator',
    'services/salon/salon-management-backend/Dockerfile.migrator',
    'services/staff/staff-management-backend/Dockerfile.migrator',
    'services/treatment/treatment-management-backend/Dockerfile.migrator',
    'services/public-identity/public-identity-management-backend/Dockerfile.migrator',
]

def update_dockerfile(file_path):
    """Update Dockerfile.migrator to use Bun 1.3.5 and Prisma 7.2.0"""
    root = Path('/private/var/www/2025/ollamar1/beauty-crm')
    dockerfile_path = root / file_path
    
    if not dockerfile_path.exists():
        return False, "File not found"
    
    with open(dockerfile_path, 'r') as f:
        content = f.read()
    
    # Update Bun version from any version to 1.3.5
    content = re.sub(
        r'FROM oven/bun:[0-9.]+(-alpine)?',
        'FROM oven/bun:1.3.5-alpine',
        content
    )
    
    # Update Prisma versions to 7.2.0
    content = re.sub(
        r'"@prisma/client":"[^"]*"',
        '"@prisma/client":"^7.2.0"',
        content
    )
    content = re.sub(
        r'"prisma":"[^"]*"',
        '"prisma":"^7.2.0"',
        content
    )
    
    with open(dockerfile_path, 'w') as f:
        f.write(content)
    
    return True, "Updated"

def main():
    print("Updating all Dockerfile.migrator files for Prisma v7.2.0...")
    print()
    print("Prisma v7.2.0 requires Node.js 20.19+, 22.12+, or 24.0+")
    print("Upgrading to Bun 1.3.5 (includes Node.js 22.x)")
    print()
    
    updated_count = 0
    failed_count = 0
    
    for file_path in MIGRATOR_FILES:
        service_name = file_path.split('/')[-2]
        
        success, message = update_dockerfile(file_path)
        
        if success:
            print(f"✅ {service_name:45} → Bun 1.3.5 + Prisma 7.2.0")
            updated_count += 1
        else:
            print(f"❌ {service_name:45} → {message}")
            failed_count += 1
    
    print()
    print(f"✅ Updated {updated_count}/{len(MIGRATOR_FILES)} Dockerfile.migrator files")
    
    if failed_count > 0:
        print(f"⚠️  {failed_count} files failed to update")

if __name__ == '__main__':
    main()

