#!/bin/bash

cd /private/var/www/2025/ollamar1/beauty-crm

echo "╔════════════════════════════════════════════════════════════╗"
echo "║     Verifying Prisma v7.2.0 Build Status                  ║"
echo "╚════════════════════════════════════════════════════════════╝"
echo ""

# List of all services with Prisma
services=(
  "shared-platform-engineering/platform-db-client"
  "shared-platform-engineering/neural-mcp"
  "shared-ddd-layers/infrastructure"
  "services/salon/salon-management-backend"
  "services/staff/staff-management-backend"
  "services/treatment/treatment-management-backend"
  "services/appointment/appointment-management-backend"
  "services/appointment/appointment-planner-backend"
  "services/public-identity/public-identity-management-backend"
  "services/inventory/inventory-management-backend"
)

success_count=0
fail_count=0

for service in "${services[@]}"; do
  service_name=$(basename "$service")
  
  # Check if dist directory exists and has files
  if [ -d "$service/dist" ] && [ "$(ls -A $service/dist 2>/dev/null)" ]; then
    echo "✅ $service_name"
    ((success_count++))
  else
    echo "❌ $service_name (no dist/ or empty)"
    ((fail_count++))
  fi
done

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "Summary: $success_count successful, $fail_count failed"
echo ""

if [ $fail_count -eq 0 ]; then
  echo "✅ All services built successfully with Prisma v7.2.0!"
else
  echo "⚠️  Some services failed to build. Rebuilding failed services..."
  echo ""
  
  for service in "${services[@]}"; do
    service_name=$(basename "$service")
    
    if [ ! -d "$service/dist" ] || [ ! "$(ls -A $service/dist 2>/dev/null)" ]; then
      echo "Rebuilding $service_name..."
      cd "$service"
      bun install && bunx prisma generate && bun run build
      if [ $? -eq 0 ]; then
        echo "✅ $service_name rebuilt successfully"
      else
        echo "❌ $service_name rebuild failed"
      fi
      cd /private/var/www/2025/ollamar1/beauty-crm
      echo ""
    fi
  done
fi

