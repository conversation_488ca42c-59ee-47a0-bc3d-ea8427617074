#!/usr/bin/env python3
"""
Update all Dockerfile.migrator files to:
1. Use Bun 1.3.5
2. Install Node.js 22 on top of Bun for Prisma v7.2.0 compatibility
"""

import re
from pathlib import Path

MIGRATOR_FILES = [
    'services/appointment/appointment-planner-backend/Dockerfile.migrator',
    'services/appointment/appointment-management-backend/Dockerfile.migrator',
    'services/salon/salon-management-backend/Dockerfile.migrator',
    'services/staff/staff-management-backend/Dockerfile.migrator',
    'services/treatment/treatment-management-backend/Dockerfile.migrator',
    'services/public-identity/public-identity-management-backend/Dockerfile.migrator',
]

def update_dockerfile(file_path):
    """Update Dockerfile to use Bun 1.3.5 + Node.js 22"""
    root = Path('/private/var/www/2025/ollamar1/beauty-crm')
    dockerfile_path = root / file_path
    
    if not dockerfile_path.exists():
        return False, "File not found"
    
    with open(dockerfile_path, 'r') as f:
        content = f.read()
    
    # Update FROM line to use Bun 1.3.5
    content = re.sub(
        r'FROM oven/bun:[0-9.]+(-alpine)?',
        'FROM oven/bun:1.3.5-alpine',
        content
    )
    
    # Update the RUN apk line to include nodejs and npm
    content = re.sub(
        r'RUN apk add --no-cache postgresql-client',
        'RUN apk add --no-cache postgresql-client nodejs npm',
        content
    )
    
    # Add comment about Node.js requirement if not present
    if 'Install Node.js 22' not in content:
        content = re.sub(
            r'# Install dependencies for database operations',
            '# Install Node.js 22 (required for Prisma v7.2.0) and PostgreSQL client',
            content
        )
    
    with open(dockerfile_path, 'w') as f:
        f.write(content)
    
    return True, "Updated"

def main():
    print("Updating all Dockerfile.migrator files...")
    print("Adding Node.js 22 on top of Bun 1.3.5 for Prisma v7.2.0 compatibility")
    print()
    
    updated_count = 0
    
    for file_path in MIGRATOR_FILES:
        service_name = file_path.split('/')[-2]
        
        success, message = update_dockerfile(file_path)
        
        if success:
            print(f"✅ {service_name:45} → Bun 1.3.5 + Node.js 22")
            updated_count += 1
        else:
            print(f"❌ {service_name:45} → {message}")
    
    print()
    print(f"✅ Updated {updated_count}/{len(MIGRATOR_FILES)} Dockerfile.migrator files")
    print()
    print("Changes made:")
    print("  • Base image: oven/bun:1.3.5-alpine")
    print("  • Added: nodejs npm (Node.js 22.x from Alpine repos)")
    print("  • Prisma v7.2.0 will now use Node.js 22 for compatibility")

if __name__ == '__main__':
    main()

