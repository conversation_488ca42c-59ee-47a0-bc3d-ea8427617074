#!/bin/bash

cd /private/var/www/2025/ollamar1/beauty-crm

echo "╔════════════════════════════════════════════════════════════╗"
echo "║     Prisma Config Database Mappings                       ║"
echo "╚════════════════════════════════════════════════════════════╝"
echo ""

echo "platform-db-client                            → beauty_crm"
grep "5432/" shared-platform-engineering/platform-db-client/prisma.config.ts | grep -o "beauty_crm[^?]*" | head -1

echo "neural-mcp                                    → beauty_crm_neural"
grep "5432/" shared-platform-engineering/neural-mcp/prisma.config.ts | grep -o "beauty_crm[^?]*" | head -1

echo "salon-management-backend                      → beauty_crm_salon"
grep "5432/" services/salon/salon-management-backend/prisma.config.ts | grep -o "beauty_crm[^?]*" | head -1

echo "staff-management-backend                      → beauty_crm_staff"
grep "5432/" services/staff/staff-management-backend/prisma.config.ts | grep -o "beauty_crm[^?]*" | head -1

echo "treatment-management-backend                  → beauty_crm_treatment"
grep "5432/" services/treatment/treatment-management-backend/prisma.config.ts | grep -o "beauty_crm[^?]*" | head -1

echo "appointment-management-backend                → beauty_crm_appointment"
grep "5432/" services/appointment/appointment-management-backend/prisma.config.ts | grep -o "beauty_crm[^?]*" | head -1

echo "appointment-planner-backend                   → beauty_crm_planner"
grep "5432/" services/appointment/appointment-planner-backend/prisma.config.ts | grep -o "beauty_crm[^?]*" | head -1

echo "public-identity-management-backend            → beauty_crm_identity"
grep "5432/" services/public-identity/public-identity-management-backend/prisma.config.ts | grep -o "beauty_crm[^?]*" | head -1

echo ""
echo "✅ All prisma.config.ts files configured with service-specific databases"

