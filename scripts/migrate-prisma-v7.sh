#!/bin/bash

# Prisma v7.2.0 Migration Script
# This script automates the migration of all Prisma packages to v7.2.0

set -e

PRISMA_VERSION="7.2.0"
REPO_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "╔════════════════════════════════════════════════════════════╗"
echo "║     Prisma v7.2.0 Migration Script                        ║"
echo "║     Beauty CRM Monorepo                                    ║"
echo "╚════════════════════════════════════════════════════════════╝"
echo ""
echo "Repository: $REPO_ROOT"
echo "Target Version: $PRISMA_VERSION"
echo ""

# Function to update package
update_package() {
  local pkg_dir=$1
  local pkg_name=$2
  
  echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
  echo "📦 Updating: $pkg_name"
  echo "   Path: $pkg_dir"
  echo ""
  
  cd "$pkg_dir"
  
  # Check if package.json has Prisma dependencies
  if grep -q '"@prisma/client"' package.json; then
    echo "   ⬆️  Updating @prisma/client to ^$PRISMA_VERSION..."
    bun add "@prisma/client@^$PRISMA_VERSION" 2>&1 | tail -3
  fi
  
  if grep -q '"prisma"' package.json; then
    echo "   ⬆️  Updating prisma to ^$PRISMA_VERSION..."
    bun add -D "prisma@^$PRISMA_VERSION" 2>&1 | tail -3
  fi
  
  # Generate Prisma client if schema exists
  if [ -f "prisma/schema.prisma" ]; then
    echo "   🔧 Generating Prisma client..."
    bunx prisma generate 2>&1 | grep -E "(Generated|Prisma Client)" | head -3 || true
  fi
  
  # Test build if build script exists
  if grep -q '"build"' package.json; then
    echo "   🏗️  Testing build..."
    bun run build 2>&1 | tail -3 || {
      echo "   ⚠️  Build failed - manual intervention needed"
      return 1
    }
  fi
  
  echo "   ✅ $pkg_name updated successfully"
  echo ""
  
  cd "$REPO_ROOT"
}

# Confirm before proceeding
echo "⚠️  This script will update Prisma to v$PRISMA_VERSION in all packages."
echo ""
read -p "Continue? (y/N) " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo "❌ Migration cancelled"
  exit 1
fi

echo ""
echo "🚀 Starting migration..."
echo ""

# Phase 1: Update platform packages
echo "═══════════════════════════════════════════════════════════"
echo "PHASE 1: Platform Packages"
echo "═══════════════════════════════════════════════════════════"
echo ""

update_package "shared-platform-engineering/platform-db-client" "@beauty-crm/platform-db-client"
update_package "shared-ddd-layers/infrastructure" "@beauty-crm/infrastructure"
update_package "shared-platform-engineering/neural-mcp" "@beauty-crm/neural-mcp"

# Phase 2: Update service backends
echo "═══════════════════════════════════════════════════════════"
echo "PHASE 2: Service Backends"
echo "═══════════════════════════════════════════════════════════"
echo ""

update_package "services/salon/salon-management-backend" "@beauty-crm/salon-management-backend"
update_package "services/staff/staff-management-backend" "@beauty-crm/staff-management-backend"
update_package "services/treatment/treatment-management-backend" "@beauty-crm/treatment-management-backend"
update_package "services/appointment/appointment-management-backend" "@beauty-crm/appointment-management-backend"
update_package "services/appointment/appointment-planner-backend" "@beauty-crm/appointment-planner-backend"
update_package "services/public-identity/public-identity-management-backend" "@beauty-crm/public-identity-management-backend"

# Phase 3: Republish platform packages to Verdaccio
echo "═══════════════════════════════════════════════════════════"
echo "PHASE 3: Republish to Verdaccio"
echo "═══════════════════════════════════════════════════════════"
echo ""

echo "📦 Publishing platform-db-client..."
cd shared-platform-engineering/platform-db-client
npm publish --registry http://verdaccio.localhost:4873 --access public 2>&1 | grep -E "(Published|409)" || true
cd "$REPO_ROOT"

echo "📦 Publishing infrastructure..."
cd shared-ddd-layers/infrastructure
npm publish --registry http://verdaccio.localhost:4873 --access public 2>&1 | grep -E "(Published|409)" || true
cd "$REPO_ROOT"

# Phase 4: Summary
echo ""
echo "═══════════════════════════════════════════════════════════"
echo "MIGRATION SUMMARY"
echo "═══════════════════════════════════════════════════════════"
echo ""

echo "✅ Platform packages updated and published"
echo "✅ Service backends updated"
echo ""

echo "📋 Next Steps:"
echo "   1. Review PRISMA_V7_MIGRATION_TODO.md for detailed checklist"
echo "   2. Run tests: bun test (in each service)"
echo "   3. Test Docker builds: docker-compose build"
echo "   4. Test with Tilt: tilt up"
echo "   5. Verify all services are healthy"
echo ""

echo "🔍 Verify versions:"
echo "   grep -r '\"@prisma/client\"' --include=\"package.json\" . | grep -v node_modules"
echo ""

echo "✨ Migration complete!"

