#!/usr/bin/env python3
"""
Migrate all Prisma schema files to v7 format
- Remove `url` from datasource blocks
- Create prisma.config.ts files
- Create .env files with placeholder DATABASE_URL
"""

import os
import re
from pathlib import Path

PRISMA_CONFIG_TEMPLATE = '''import { defineConfig } from 'prisma/config';

export default defineConfig({
  schema: 'prisma/schema.prisma',
  datasource: {
    url: process.env.DATABASE_URL || 'postgresql://user:password@localhost:5432/beauty_crm?schema=public',
  },
});
'''

ENV_TEMPLATE = '''# Placeholder DATABASE_URL for Prisma client generation
# Actual connection URL should be provided at runtime
DATABASE_URL="postgresql://user:password@localhost:5432/beauty_crm?schema=public"
'''

def update_schema_file(schema_path):
    """Remove url from datasource block in schema.prisma"""
    with open(schema_path, 'r') as f:
        content = f.read()
    
    # Remove url line from datasource block
    pattern = r'(datasource\s+\w+\s*\{[^}]*?)\s*url\s*=\s*env\([^)]+\)\s*\n'
    new_content = re.sub(pattern, r'\1\n', content)
    
    if new_content != content:
        with open(schema_path, 'w') as f:
            f.write(new_content)
        return True
    return False

def create_prisma_config(package_dir):
    """Create prisma.config.ts file"""
    config_path = package_dir / 'prisma.config.ts'
    if not config_path.exists():
        with open(config_path, 'w') as f:
            f.write(PRISMA_CONFIG_TEMPLATE)
        return True
    return False

def create_env_file(package_dir):
    """Create .env file with placeholder DATABASE_URL"""
    env_path = package_dir / '.env'
    if not env_path.exists():
        with open(env_path, 'w') as f:
            f.write(ENV_TEMPLATE)
        return True
    return False

def main():
    root = Path('/private/var/www/2025/ollamar1/beauty-crm')
    
    print("Migrating to Prisma v7...")
    print()
    
    # Find all schema.prisma files
    schema_files = list(root.rglob('schema.prisma'))
    schema_files = [f for f in schema_files if 'node_modules' not in str(f)]
    
    for schema_file in schema_files:
        package_dir = schema_file.parent.parent
        package_name = package_dir.name
        
        print(f"📦 {package_name}")
        
        # Update schema file
        if update_schema_file(schema_file):
            print(f"   ✅ Updated schema.prisma (removed url)")
        else:
            print(f"   ℹ️  schema.prisma already updated")
        
        # Create prisma.config.ts
        if create_prisma_config(package_dir):
            print(f"   ✅ Created prisma.config.ts")
        else:
            print(f"   ℹ️  prisma.config.ts already exists")
        
        # Create .env
        if create_env_file(package_dir):
            print(f"   ✅ Created .env")
        else:
            print(f"   ℹ️  .env already exists")
        
        print()
    
    print(f"✅ Migrated {len(schema_files)} packages to Prisma v7")

if __name__ == '__main__':
    main()

