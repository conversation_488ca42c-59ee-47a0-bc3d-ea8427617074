#!/bin/bash
set -e

cd /private/var/www/2025/ollamar1/beauty-crm

echo "╔════════════════════════════════════════════════════════════╗"
echo "║     Building All Packages with Prisma v7.2.0              ║"
echo "╚════════════════════════════════════════════════════════════╝"
echo ""

# Phase 1: Platform Packages
echo "Phase 1: Platform Packages"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Platform DB Client
echo "📦 1/3 platform-db-client"
cd shared-platform-engineering/platform-db-client
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ platform-db-client complete"
cd ../..

# Infrastructure (DDD)
echo "📦 2/3 infrastructure"
cd shared-ddd-layers/infrastructure
bun install > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ infrastructure complete"
cd ../..

# Neural MCP
echo "📦 3/3 neural-mcp"
cd shared-platform-engineering/neural-mcp
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ neural-mcp complete"
cd ../..

echo ""
echo "Phase 2: Service Backends"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Salon
echo "📦 1/7 salon-management-backend"
cd services/salon/salon-management-backend
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ salon-management-backend complete"
cd ../../..

# Staff
echo "📦 2/7 staff-management-backend"
cd services/staff/staff-management-backend
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ staff-management-backend complete"
cd ../../..

# Treatment
echo "📦 3/7 treatment-management-backend"
cd services/treatment/treatment-management-backend
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ treatment-management-backend complete"
cd ../../..

# Appointment Management
echo "📦 4/7 appointment-management-backend"
cd services/appointment/appointment-management-backend
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ appointment-management-backend complete"
cd ../../..

# Appointment Planner
echo "📦 5/7 appointment-planner-backend"
cd services/appointment/appointment-planner-backend
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ appointment-planner-backend complete"
cd ../../..

# Public Identity
echo "📦 6/7 public-identity-management-backend"
cd services/public-identity/public-identity-management-backend
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ public-identity-management-backend complete"
cd ../../..

# Inventory
echo "📦 7/7 inventory-management-backend"
cd services/inventory/inventory-management-backend
bun install > /dev/null 2>&1
bunx prisma generate > /dev/null 2>&1
bun run build > /dev/null 2>&1
echo "✅ inventory-management-backend complete"
cd ../../..

echo ""
echo "╔════════════════════════════════════════════════════════════╗"
echo "║     Migration Complete!                                    ║"
echo "╚════════════════════════════════════════════════════════════╝"
echo ""
echo "✅ All packages updated to Prisma v7.2.0"
echo "✅ All Prisma clients generated"
echo "✅ All builds successful"
echo ""
echo "Next steps:"
echo "  1. Publish platform packages to Verdaccio"
echo "  2. Test with Docker builds"
echo "  3. Test with Tilt"

