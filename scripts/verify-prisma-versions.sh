#!/bin/bash

# Verify Prisma versions across the monorepo

REPO_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "╔════════════════════════════════════════════════════════════╗"
echo "║     Prisma Version Verification                            ║"
echo "╚════════════════════════════════════════════════════════════╝"
echo ""

cd "$REPO_ROOT"

echo "📊 Current Prisma Versions Across Monorepo"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "Package                                    @prisma/client    prisma"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Find all package.json files with Prisma dependencies
find . -name "package.json" -not -path "*/node_modules/*" -not -path "*/.next/*" | while read pkg_file; do
  pkg_dir=$(dirname "$pkg_file")
  pkg_name=$(grep '"name"' "$pkg_file" | head -1 | sed 's/.*"name": "\([^"]*\)".*/\1/')
  
  # Check for @prisma/client
  client_version=$(grep '"@prisma/client"' "$pkg_file" | sed 's/.*"@prisma\/client": "\([^"]*\)".*/\1/' || echo "")
  
  # Check for prisma
  prisma_version=$(grep '"prisma"' "$pkg_file" | grep -v "@prisma/client" | sed 's/.*"prisma": "\([^"]*\)".*/\1/' || echo "")
  
  # Only show packages that have Prisma
  if [ -n "$client_version" ] || [ -n "$prisma_version" ]; then
    printf "%-42s %-17s %-17s\n" "$pkg_name" "${client_version:-N/A}" "${prisma_version:-N/A}"
  fi
done

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

echo "📁 Prisma Schema Files"
echo ""
find . -name "schema.prisma" -not -path "*/node_modules/*" | while read schema_file; do
  echo "   📄 $schema_file"
  
  # Check generator version if specified
  if grep -q "engineType" "$schema_file"; then
    echo "      ⚙️  Custom engine type configured"
  fi
  
  # Check for preview features
  if grep -q "previewFeatures" "$schema_file"; then
    features=$(grep "previewFeatures" "$schema_file" | sed 's/.*previewFeatures = \[\(.*\)\].*/\1/')
    echo "      🔬 Preview features: $features"
  fi
done

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Check for version mismatches
echo "🔍 Version Analysis"
echo ""

# Count unique versions
client_versions=$(find . -name "package.json" -not -path "*/node_modules/*" -exec grep -h '"@prisma/client"' {} \; | sed 's/.*"@prisma\/client": "\([^"]*\)".*/\1/' | sort -u)
prisma_versions=$(find . -name "package.json" -not -path "*/node_modules/*" -exec grep -h '"prisma"' {} \; | grep -v "@prisma/client" | sed 's/.*"prisma": "\([^"]*\)".*/\1/' | sort -u)

echo "Unique @prisma/client versions:"
echo "$client_versions" | while read version; do
  if [ -n "$version" ]; then
    count=$(find . -name "package.json" -not -path "*/node_modules/*" -exec grep -l "\"@prisma/client\": \"$version\"" {} \; | wc -l)
    echo "   $version ($count packages)"
  fi
done

echo ""
echo "Unique prisma versions:"
echo "$prisma_versions" | while read version; do
  if [ -n "$version" ]; then
    count=$(find . -name "package.json" -not -path "*/node_modules/*" -exec grep -l "\"prisma\": \"$version\"" {} \; | wc -l)
    echo "   $version ($count packages)"
  fi
done

echo ""

# Check if all on v7.2.0
if echo "$client_versions" | grep -q "^7.2.0" && ! echo "$client_versions" | grep -qv "^7.2.0"; then
  echo "✅ All packages are on Prisma v7.2.0!"
else
  echo "⚠️  Not all packages are on Prisma v7.2.0 yet"
  echo ""
  echo "To migrate, run:"
  echo "   bash scripts/migrate-prisma-v7.sh"
fi

echo ""

