#!/usr/bin/env python3
"""
Update all prisma.config.ts files with correct database names
"""

from pathlib import Path

# Mapping of service paths to their database names
DATABASE_MAPPINGS = {
    'shared-platform-engineering/platform-db-client': 'beauty_crm',
    'shared-platform-engineering/neural-mcp': 'beauty_crm_neural',
    'services/salon/salon-management-backend': 'beauty_crm_salon',
    'services/staff/staff-management-backend': 'beauty_crm_staff',
    'services/treatment/treatment-management-backend': 'beauty_crm_treatment',
    'services/appointment/appointment-management-backend': 'beauty_crm_appointment',
    'services/appointment/appointment-planner-backend': 'beauty_crm_planner',
    'services/public-identity/public-identity-management-backend': 'beauty_crm_identity',
    'services/inventory/inventory-management-backend': 'beauty_crm_inventory',
}

CONFIG_TEMPLATE = """import {{ defineConfig }} from 'prisma/config';

export default defineConfig({{
  schema: 'prisma/schema.prisma',
  datasource: {{
    url:
      process.env.DATABASE_URL ||
      'postgresql://user:password@localhost:5432/{db_name}?schema=public',
  }},
}});
"""

def update_config_file(service_path, db_name):
    """Update prisma.config.ts with correct database name"""
    root = Path('/private/var/www/2025/ollamar1/beauty-crm')
    config_path = root / service_path / 'prisma.config.ts'
    
    if not config_path.exists():
        print(f"⚠️  {service_path}: prisma.config.ts not found")
        return False
    
    content = CONFIG_TEMPLATE.format(db_name=db_name)
    
    with open(config_path, 'w') as f:
        f.write(content)
    
    return True

def main():
    print("Updating all prisma.config.ts files with correct database names...")
    print()
    
    updated_count = 0
    
    for service_path, db_name in DATABASE_MAPPINGS.items():
        service_name = service_path.split('/')[-1]
        
        if update_config_file(service_path, db_name):
            print(f"✅ {service_name:40} → {db_name}")
            updated_count += 1
        else:
            print(f"❌ {service_name:40} → Failed")
    
    print()
    print(f"✅ Updated {updated_count}/{len(DATABASE_MAPPINGS)} prisma.config.ts files")

if __name__ == '__main__':
    main()

