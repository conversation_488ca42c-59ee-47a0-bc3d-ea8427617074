# version: '3.8'

# YAML anchors for common configurations
x-common-resources: &common-resources
  mem_limit: 256M
  cpus: 0.25
  mem_reservation: 128M

services:
  verdaccio:
    image: verdaccio/verdaccio:latest
    container_name: beauty-crm-verdaccio
    ports:
      - "4873:4873"
    volumes:
      - verdaccio-storage:/verdaccio/storage
      - verdaccio-conf:/verdaccio/conf
      - verdaccio-plugins:/verdaccio/plugins
      - ./verdaccio/config.yaml:/verdaccio/conf/config.yaml:ro
    environment:
      - VERDACCIO_PROTOCOL=http
      - VERDACCIO_PORT=4873
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.verdaccio.rule=Host(`verdaccio.localhost`)"
      - "traefik.http.routers.verdaccio.entrypoints=web"
      - "traefik.http.services.verdaccio.loadbalancer.server.port=4873"
      - "traefik.docker.network=beauty_crm_traefik-private"
    networks:
      - verdaccio-network
      - traefik-private
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:4873/-/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    <<: *common-resources

networks:
  verdaccio-network:
    name: beauty_crm_verdaccio-network
    driver: bridge
  traefik-private:
    external: true
    name: beauty_crm_traefik-private

volumes:
  verdaccio-storage:
    driver: local
  verdaccio-conf:
    driver: local
  verdaccio-plugins:
    driver: local

