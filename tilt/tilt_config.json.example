{"description": "Beauty CRM Tilt Configuration", "version": "1.0.0", "default_services": {"databases": true, "proxy": true, "monitoring": false, "api_gateway": false, "salon": true, "treatment": true, "staff": false, "appointment": true, "identity": false, "elk": false, "verdaccio": false, "infisical": false}, "service_groups": {"core": ["databases", "proxy", "salon", "treatment", "appointment"], "full": ["databases", "proxy", "monitoring", "api_gateway", "salon", "treatment", "staff", "appointment"], "minimal": ["databases", "salon"], "monitoring": ["databases", "proxy", "monitoring", "elk"], "gateway": ["databases", "proxy", "api_gateway", "salon", "treatment", "appointment"]}, "port_mappings": {"traefik_dashboard": "8080", "traefik_http": "80", "tilt_ui": "10350", "api_gateway_http": "8090", "api_gateway_dashboard": "8091", "postgres": "5432", "redis": "6379", "nats_client": "4222", "nats_monitoring": "8222", "verdaccio": "4873", "infisical": "8080"}, "access_urls": {"main_app": "http://beauty-crm.localhost", "traefik_dashboard": "http://traefik.localhost", "tilt_ui": "http://tilt.localhost", "dashy_dashboard": "http://dashboard.localhost", "nats_monitoring": "http://nats.localhost", "salon_management": "http://salon.localhost", "treatment_management": "http://treatment.localhost", "appointment_management": "http://appointment.localhost", "verdaccio": "http://verdaccio.localhost", "infisical": "http://infisical.localhost"}, "development_commands": {"test_all": "bun test", "lint_all": "bun run lint", "build_shared": "bun run build", "format_code": "bun run format"}, "docker_compose_files": {"databases": "./services/orchestration/docker-compose.databases.yml", "proxy": "./services/orchestration/docker-compose.proxy.yml", "monitoring": "./services/orchestration/docker-compose.apm.yml", "api_gateway": "./services/orchestration/docker-compose.api-gateway.yml", "salon": "./services/salon/docker-compose.app.yml", "treatment": "./services/treatment/docker-compose.app.yml", "staff": "./services/staff/docker-compose.app.yml", "appointment_internal": "./services/appointment/docker-compose.app.yml", "appointment_planner": "./services/appointment/docker-compose.planner.yml", "identity": "./identity-compose.yml", "elk": "./elk-compose.yml", "verdaccio": "./docker-compose.verdaccio.yml", "infisical": "./docker-compose.infisical.yml"}, "live_update_paths": {"backend_src": "/app/src", "frontend_src": "/app/src", "frontend_public": "/app/public"}, "health_check_endpoints": {"salon_backend": "/health", "treatment_backend": "/health", "staff_backend": "/health", "appointment_backend": "/health"}, "resource_dependencies": {"databases": [], "proxy": [], "monitoring": [], "api_gateway": ["proxy"], "salon_backend": ["databases"], "salon_frontend": ["salon_backend"], "treatment_backend": ["databases"], "treatment_frontend": ["treatment_backend"], "staff_backend": ["databases"], "staff_frontend": ["staff_backend"], "appointment_backend": ["databases"], "appointment_frontend": ["appointment_backend"]}, "labels": {"infrastructure": ["databases", "proxy", "api_gateway"], "application": ["salon", "treatment", "staff", "appointment"], "monitoring": ["monitoring", "elk"], "backend": ["salon_backend", "treatment_backend", "staff_backend", "appointment_backend"], "frontend": ["salon_frontend", "treatment_frontend", "staff_frontend", "appointment_frontend"]}}