# Prisma v7 Migration - Rollback Plan

## 🔄 Rollback Strategy

If issues arise during or after the Prisma v7 migration, use this guide to safely rollback.

## ⚠️ When to Rollback

Consider rollback if:
- Critical build failures that can't be quickly resolved
- Database migration issues
- Production incidents related to Prisma changes
- Significant performance degradation
- Breaking changes affecting core functionality

## 📋 Pre-Rollback Checklist

- [ ] Document the issue(s) encountered
- [ ] Capture error logs and stack traces
- [ ] Note which packages/services are affected
- [ ] Verify git branch state
- [ ] Check if database migrations were run

## 🔙 Rollback Methods

### Method 1: Git Revert (Recommended)

If you created a migration branch:

```bash
# Switch back to main/previous branch
git checkout main

# Or reset the migration branch
git checkout feat/prisma-v7-migration
git reset --hard pre-prisma-v7-migration  # Use the tag you created

# Force push if needed (be careful!)
git push origin feat/prisma-v7-migration --force
```

### Method 2: Manual Package Downgrade

Downgrade packages to previous versions:

```bash
# Platform packages
cd shared-platform-engineering/platform-db-client
bun add @prisma/client@^6.9.0 -D prisma@^6.9.0
bunx prisma generate
bun run build

cd ../../shared-ddd-layers/infrastructure
bun add @prisma/client@^6.18.0
bunx prisma generate
bun run build

# Services
cd ../../services/salon/salon-management-backend
bun add @prisma/client@^6.9.0 -D prisma@^6.9.0
bunx prisma generate

cd ../../staff/staff-management-backend
bun add @prisma/client@^6.18.0 -D prisma@^6.18.0
bunx prisma generate

cd ../../treatment/treatment-management-backend
bun add @prisma/client@^6.18.0 -D prisma@^6.19.1
bunx prisma generate

cd ../../appointment/appointment-management-backend
bun add @prisma/client@^6.18.0 -D prisma@^6.18.0
bunx prisma generate
```

### Method 3: Restore from Backup

If you backed up package.json files:

```bash
# Restore all package.json files
find . -name "package.json.backup" | while read backup; do
  original="${backup%.backup}"
  cp "$backup" "$original"
  echo "Restored $original"
done

# Reinstall dependencies
bun install

# Regenerate Prisma clients
find . -name "schema.prisma" -not -path "*/node_modules/*" | while read schema; do
  dir=$(dirname "$schema")
  cd "$dir/.."
  bunx prisma generate
  cd -
done
```

## 🗄️ Database Rollback

### If Migrations Were Run

```bash
# Check migration status
bunx prisma migrate status

# Rollback last migration (if supported)
bunx prisma migrate resolve --rolled-back <migration_name>

# Or restore from database backup
# (Depends on your backup strategy)
```

### If Schema Changes Were Made

```bash
# Revert schema.prisma files
git checkout HEAD -- services/*/prisma/schema.prisma
git checkout HEAD -- shared-platform-engineering/*/prisma/schema.prisma

# Regenerate clients
bunx prisma generate
```

## 🐳 Docker Rollback

### Rebuild with Previous Versions

```bash
# Stop current containers
docker-compose down

# Clear Docker cache
docker system prune -af

# Rebuild with previous code
docker-compose build --no-cache

# Start services
docker-compose up -d
```

### Use Previous Images

```bash
# If you tagged images before migration
docker tag salon_salon-management-backend:latest salon_salon-management-backend:pre-v7
docker tag staff_staff-management-backend:latest staff_staff-management-backend:pre-v7

# Rollback
docker tag salon_salon-management-backend:pre-v7 salon_salon-management-backend:latest
docker tag staff_staff-management-backend:pre-v7 staff_staff-management-backend:latest
```

## 📦 Verdaccio Rollback

### Republish Previous Versions

```bash
# If you have previous versions in Verdaccio
cd shared-platform-engineering/platform-db-client

# Unpublish v7 version
npm unpublish @beauty-crm/platform-db-client@1.0.2 \
  --registry http://verdaccio.localhost:4873 --force

# Republish previous version
git checkout HEAD~1 -- package.json
bun install
bun run build
npm publish --registry http://verdaccio.localhost:4873 --access public
```

## ✅ Post-Rollback Verification

After rollback, verify:

- [ ] All package.json files show correct Prisma versions
- [ ] All builds pass
- [ ] Prisma clients generated successfully
- [ ] Docker images build
- [ ] Tilt stack starts
- [ ] All services healthy
- [ ] Database connections work
- [ ] API endpoints respond
- [ ] Tests pass

### Verification Commands

```bash
# Check versions
bash scripts/verify-prisma-versions.sh

# Test builds
cd shared-platform-engineering/platform-db-client && bun run build
cd services/salon/salon-management-backend && bun run build

# Test Tilt
tilt down
tilt up

# Test API
curl http://localhost:4000/health
```

## 📝 Post-Rollback Actions

1. **Document the Issue**
   - Create GitHub issue with details
   - Include error logs
   - Note affected services
   - Describe attempted fixes

2. **Communicate**
   - Notify team of rollback
   - Update status in project management
   - Schedule post-mortem if needed

3. **Plan Next Steps**
   - Analyze root cause
   - Research solutions
   - Plan retry strategy
   - Update migration plan

## 🔍 Common Issues & Solutions

### Issue: Version Conflicts

```bash
# Clear all node_modules and lock files
find . -name "node_modules" -type d -prune -exec rm -rf {} +
find . -name "bun.lockb" -delete

# Reinstall
bun install
```

### Issue: Prisma Client Not Found

```bash
# Regenerate all Prisma clients
find . -name "schema.prisma" -not -path "*/node_modules/*" | while read schema; do
  dir=$(dirname "$schema")
  cd "$dir/.."
  rm -rf node_modules/.prisma
  bunx prisma generate
  cd -
done
```

### Issue: Docker Build Fails

```bash
# Clear Docker build cache
docker builder prune -af

# Rebuild specific service
docker-compose build --no-cache salon-management-backend
```

## 📞 Escalation

If rollback fails or issues persist:

1. Check git history for last known good state
2. Restore from system backups if available
3. Contact Prisma support if Prisma-specific issue
4. Review Prisma GitHub issues for similar problems

## 🎯 Success Criteria

Rollback is successful when:
- ✅ All services back to previous Prisma versions
- ✅ All builds passing
- ✅ All tests passing
- ✅ Production services stable
- ✅ No data loss
- ✅ Team notified and aligned

---

**Remember**: Always test rollback procedures in a non-production environment first!

