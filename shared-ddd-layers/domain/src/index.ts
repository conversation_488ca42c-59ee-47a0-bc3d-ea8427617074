// Export DTOs first as they have no dependencies

// Export aggregates
export * from './aggregates';
export * from './dto/api';
export * from './dto/db';
export * from './dto/queue';
export type {
  CreateSalonAPIOutput,
  CreateSalonDBInput,
  CreateSalonQueueInput,
  CreateSalonRequestInput,
  SalonAPIResponse,
  SalonDBOutput,
  SalonQueueOutput,
  SalonStatus,
  UpdateSalonDBInput,
  validateCreateSalonRequestInput,
} from './dto/salon.dto';
// Export Salon interface separately to avoid conflict with Salon class
export type { Salon as SalonDTO } from './dto/salon.dto';
export type {
  CreateUserAPIOutput,
  CreateUserDBInput,
  CreateUserQueueInput,
  CreateUserRequestInput,
  UpdateUserDBInput,
  UserAPIResponse,
  UserDBOutput,
  UserQueueOutput,
  validateCreateUserRequestInput,
} from './dto/user.dto';
// Domain layer exports
export * as entities from './entities';
// Export domain entities and value objects
export {
  Entity,
  Salon,
  SalonEntity,
  User,
  UserEntity,
} from './entities';
// Re-export UserRole from product-domain-types
export { UserRole } from '@beauty-crm/product-domain-types';
// export * from './events';
export * from './ports';
export type { ISalonRepository, IUserRepository } from './ports/repositories';
// Export interfaces and types that depend on DTOs
export * from './ports/repositories';
export * from './ports/services';
export * from './search';
export * from './search';
// Export search interfaces
export type { IBaseFilter } from './search/IBaseFilter';
export type { ISalonFilterInterface } from './search/ISalonFilterInterface';
export type { IUserFilterInterface } from './search/IUserFilterInterface';
export * from './services';
// Export domain services and events
export * from './services/user.service';
export * from './valueobjects';
// export * from './commands';

export * from './repositories/appointment-repository';
