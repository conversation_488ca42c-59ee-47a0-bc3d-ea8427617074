// Placeholder for appointment repository
// This will be implemented when appointment types are available
export interface AppointmentRepository {
  create(appointment: any): Promise<any>;
  update(appointment: any): Promise<any>;
  findById(id: string): Promise<any | null>;
  findByCustomerId(customerId: string): Promise<any[]>;
  findByStaffId(staffId: string): Promise<any[]>;
  findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date,
  ): Promise<any[]>;
  delete(id: string): Promise<void>;
}
