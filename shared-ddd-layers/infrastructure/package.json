{"dependencies": {"@beauty-crm/domain": "^1.0.0", "@beauty-crm/platform-computing-lifecycle": "^1.0.0", "@beauty-crm/platform-computing-runtime": "^1.0.0", "@beauty-crm/platform-db-client": "^1.0.4", "@beauty-crm/platform-environment-names": "^1.0.0", "@beauty-crm/platform-logger": "^1.0.3", "@beauty-crm/product-domain-types": "^1.0.0", "@beauty-crm/product-responses": "^1.0.0", "@prisma/client": "^7.2.0", "zod": "^4.3.5"}, "devDependencies": {"@types/node": "^25.0.3", "rimraf": "^6.1.2", "typescript": "^5.9.3"}, "main": "dist/index.js", "name": "@beauty-crm/infrastructure", "publishConfig": {"access": "public"}, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for infrastructure' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.1"}