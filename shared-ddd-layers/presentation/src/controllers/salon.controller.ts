import type {
  CreateSalonUseCase,
  DeleteSalonUseCase,
  GetSalonUseCase,
  UpdateSalonUseCase,
} from '@beauty-crm/application';
import {
  createErrorResponse,
  createSuccessResponse,
} from '@beauty-crm/product-responses';
import { Logger } from '@beauty-crm/platform-logger';
import { Hono } from 'hono';

interface SalonControllerDeps {
  getSalonUseCase: GetSalonUseCase;
  createSalonUseCase: CreateSalonUseCase;
  updateSalonUseCase: UpdateSalonUseCase;
  deleteSalonUseCase: DeleteSalonUseCase;
}

export class SalonController {
  public readonly routes: Hono = new Hono();

  constructor(private readonly deps: SalonControllerDeps) {
    this.setupRoutes();
  }

  private setupRoutes() {
    // Get all salons
    this.routes.get('/', async (c) => {
      try {
        const salons = await this.deps.getSalonUseCase.getAll();
        return c.json(createSuccessResponse(salons));
      } catch (error) {
        Logger.error('Error getting salons', { error });
        return c.json(createErrorResponse('Failed to get salons'), 500);
      }
    });

    // Get salon by ID
    this.routes.get('/:id', async (c) => {
      try {
        const id = c.req.param('id');
        const salon = await this.deps.getSalonUseCase.execute(id);

        if (!salon) {
          return c.json(createErrorResponse('Salon not found'), 404);
        }

        return c.json(createSuccessResponse(salon));
      } catch (error) {
        Logger.error('Error getting salon', { error });
        return c.json(createErrorResponse('Failed to get salon'), 500);
      }
    });

    // Create salon
    this.routes.post('/', async (c) => {
      try {
        const input = await c.req.json();
        const salon = await this.deps.createSalonUseCase.execute(input);
        return c.json(createSuccessResponse(salon), 201);
      } catch (error) {
        Logger.error('Error creating salon', { error });
        return c.json(createErrorResponse('Failed to create salon'), 500);
      }
    });

    // Update salon
    this.routes.put('/:id', async (c) => {
      try {
        const id = c.req.param('id');
        const input = await c.req.json();
        const salon = await this.deps.updateSalonUseCase.execute(id, input);

        if (!salon) {
          return c.json(createErrorResponse('Salon not found'), 404);
        }

        return c.json(createSuccessResponse(salon));
      } catch (error) {
        Logger.error('Error updating salon', { error });
        return c.json(createErrorResponse('Failed to update salon'), 500);
      }
    });

    // Delete salon
    this.routes.delete('/:id', async (c) => {
      try {
        const id = c.req.param('id');
        await this.deps.deleteSalonUseCase.execute(id);
        return new Response('', { status: 204 });
      } catch (error) {
        Logger.error('Error deleting salon', { error });
        return c.json(createErrorResponse('Failed to delete salon'), 500);
      }
    });
  }
}
