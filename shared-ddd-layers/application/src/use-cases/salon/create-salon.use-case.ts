import type { ISalonRepository, IUserRepository } from '@beauty-crm/domain';
import type {
  CreateSalonDBInput,
  CreateUserDBInput,
} from '@beauty-crm/domain/src/dto/db';
import type { Salon } from '@beauty-crm/domain/src/dto/salon.dto';
import { SalonStatus } from '@beauty-crm/domain/src/dto/salon.dto';

export interface CreateSalonInput {
  name: string;
  subdomain: string;
  status?: string;
}

export class CreateSalonUseCase {
  private readonly salonRepository: ISalonRepository;
  private readonly userRepository: IUserRepository;

  constructor(
    salonRepository: ISalonRepository,
    userRepository: IUserRepository,
  ) {
    this.salonRepository = salonRepository;
    this.userRepository = userRepository;
  }

  private mapInputStatus(inputStatus: string | undefined): SalonStatus {
    if (!inputStatus) return SalonStatus.ACTIVE;
    switch (inputStatus.toUpperCase()) {
      case 'ACTIVE':
        return SalonStatus.ACTIVE;
      case 'INACTIVE':
        return SalonStatus.INACTIVE;
      case 'SUSPENDED':
        return SalonStatus.SUSPENDED;
      default:
        return SalonStatus.ACTIVE;
    }
  }

  async execute(input: CreateSalonInput): Promise<Salon> {
    const dbInput: CreateSalonDBInput = {
      name: input.name,
      status: this.mapInputStatus(input.status),
      subdomain: input.subdomain,
    };
    const salon = await this.salonRepository.create(dbInput);
    const userInput: CreateUserDBInput = {
      email: `${salon.subdomain}@beauty-crm.com`,
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN' as any, // UserRole.ADMIN
    };
    await this.userRepository.create(userInput);
    return salon;
  }
}
