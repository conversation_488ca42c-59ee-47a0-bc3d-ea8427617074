import type { IUserRepository, UserDBOutput } from '@beauty-crm/domain';
import type { User } from '@beauty-crm/product-domain-types';

export class UpdateUserUseCase {
  constructor(private readonly userRepository: IUserRepository) {}

  async execute(id: string, data: Partial<User>): Promise<User | undefined> {
    const userCandidate = await this.userRepository.update(id, data);
    return userCandidate ? this.mapToUser(userCandidate) : undefined;
  }

  private mapToUser(dbUser: UserDBOutput): User {
    const [firstName, ...lastNameParts] = dbUser.email.split('@')[0].split('.');
    const lastName = lastNameParts.join('.');

    return {
      createdAt: dbUser.createdAt,
      email: dbUser.email,
      firstName,
      id: dbUser.id,
      isActive: dbUser.isActive,
      lastName,
      name: `${firstName} ${lastName}`,
      role: (dbUser.role || 'USER') as any,
      updatedAt: dbUser.updatedAt,
    };
  }
}
