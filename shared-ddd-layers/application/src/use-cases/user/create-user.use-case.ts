import type {
  CreateUserDBInput,
  IUserRepository,
  UserDBOutput,
} from '@beauty-crm/domain';
import type { User } from '@beauty-crm/product-domain-types';

export class CreateUserUseCase {
  constructor(private readonly userRepository: IUserRepository) {}

  async execute(data: CreateUserDBInput): Promise<User> {
    const user = await this.userRepository.create(data);
    return this.mapToUser(user);
  }

  private mapToUser(dbUser: UserDBOutput): User {
    const [firstName, ...lastNameParts] = dbUser.email.split('@')[0].split('.');
    const lastName = lastNameParts.join('.');

    return {
      createdAt: dbUser.createdAt,
      email: dbUser.email,
      firstName,
      id: dbUser.id,
      isActive: dbUser.isActive,
      lastName,
      name: `${firstName} ${lastName}`,
      role: 'USER' as any, // UserRole.USER
      updatedAt: dbUser.updatedAt,
    };
  }
}
