{"dependencies": {"@beauty-crm/domain": "^1.0.0", "@beauty-crm/infrastructure": "^1.0.0", "@beauty-crm/product-domain-types": "^1.0.0", "uuid": "^13.0.0"}, "devDependencies": {"@types/uuid": "^11.0.0", "rimraf": "^6.1.2", "typescript": "^5.9.3"}, "main": "dist/index.js", "name": "@beauty-crm/application", "publishConfig": {"access": "public"}, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for application' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.1"}