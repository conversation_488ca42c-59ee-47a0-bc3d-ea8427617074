# Dependencies
node_modules/
.pnp/
.pnp.js
storybook-static/
tmp/

# Testing
coverage/

# Runtime data directories
services/*/signoz/data/
services/orchestration/signoz/data/
services/orchestration/redis-data/

# prod
dist/
build/
.dev.vars

# Environment
.env
.env.local
.env.dev.local
.env.test.local
.env.prod.local

# Debug
npm-debug.log*

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# TypeScript
*.tsbuildinfo
.next

# New additions
.gen/
playwright-report/
test-results/

# Editor directories and files
.history/
.vscode/
.idea/
*.suo
*.sln
*.sw?

# Build output
node_modules/
dist/
build/
.next/
out/
coverage/

# Environment files
.env
.env.*
!.env.example
!.env.*.example

# Infisical secrets (NEVER commit these!)
.env.infisical
.infisical.json

# Logs
*.log
npm-debug.log*

# System files
.DS_Store
Thumbs.db

# Cache directories
.cache/
.turbo/

# Testing
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
*.bak


.nx/cache
.nx/workspace-data
.nx/workspace-data/file-map.json
.nx/workspace-data/project-graph.json

.yarn/

cdktf.out/


screenshots

.cursor/backups
*storybook.log


.storybook-static/

vite.config.*.timestamp*
vitest.config.*.timestamp*
/shared-product-engineering/product-domain-types/.nx/workspace-data/*.json

cucumber-reports/



.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md

# Added by Task Master AI
logs
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
.idea
.vscode
*.ntvs*
*.njsproj
# OS specific

# Task files
# tasks.json
# tasks/ 