# Beauty CRM Tilt Configuration
# Manages all Docker Compose services for local development

# Load configuration extensions
load('ext://configmap', 'configmap_create')
load('ext://restart_process', 'docker_build_with_restart')

# =============================================================================
# NETWORK INITIALIZATION
# =============================================================================
# Create required Docker networks before loading services
local_resource('init-networks',
    cmd='docker network create beauty_crm_traefik-public 2>/dev/null || true && docker network create beauty_crm_traefik-private 2>/dev/null || true && docker network create beauty_crm_backend 2>/dev/null || true && docker network create beauty_crm_database 2>/dev/null || true',
    labels=['infra.setup'],
    auto_init=True,
    resource_deps=[]
)

# Configuration flags for selective service loading
config.define_bool('database-management', usage='Enable database management services (PostgreSQL, migrators)')
config.define_bool('proxy', usage='Enable proxy services (Traefik)')
config.define_bool('monitoring', usage='Enable monitoring services (SigNoz, Dashy)')
# config.define_bool('api_gateway', usage='Enable API Gateway services') # REMOVED
config.define_bool('verdaccio', usage='Enable Verdaccio local npm registry')
config.define_bool('infisical', usage='Enable Infisical secret storage')
config.define_bool('salon', usage='Enable Salon management services')
config.define_bool('treatment', usage='Enable Treatment management services')
config.define_bool('staff', usage='Enable staff management services')
config.define_bool('appointment', usage='Enable Appointment services')
config.define_bool('backends-only', usage='Enable only backend services (skip frontends to save CPU/RAM)')
config.define_bool('identity', usage='Enable Identity services')
config.define_bool('elk', usage='Enable ELK stack')
config.define_bool('baserow', usage='Enable Baserow data platform')
config.define_bool('debezium', usage='Enable Enhanced Debezium CDC services (HTTP-based, no Kafka dependency)')

# Parse configuration
cfg = config.parse()

# Helper function to check if service should be enabled
def should_enable(service_name):
    # Default values for core services - OPTIMIZED FOR MAC M1 LOW MEMORY
    defaults = {
        'database-management': True,  # Enable PostgreSQL and migrators by default
        'proxy': True,
        'verdaccio': True,            # Enable Verdaccio by default for local package registry
        'infisical': True,             # Enable Infisical by default for secret storage
        'salon': True,                # Enable salon service by default
        'treatment': True,            # Enable treatment service by default
        'appointment': False,         # Disabled by default - enable explicitly when needed
        'staff': False,               # Disabled by default to save memory
        'backends-only': False,       # Disabled by default - enable to skip frontends
        'monitoring': False,          # Disabled by default to save memory
        'identity': False,            # Disabled by default to save memory
        'elk': False,                 # Disabled by default to save memory
        'debezium': False             # Disabled by default to save memory - enable when needed
    }
    return cfg.get(service_name, defaults.get(service_name, False))

# Resource monitoring helper
def print_resource_summary():
    enabled_services = []
    estimated_memory = 0

    # Calculate estimated memory usage based on enabled services
    memory_estimates = {
        'database-management': 1152,  # PostgreSQL(384) + migrators(128) + NATS(192) + Kafka(256) + Zookeeper(192) - reduced
        'proxy': 384,          # Traefik(256) + Nginx(64) + Dashy(64)
        'verdaccio': 128,      # Verdaccio local npm registry - lightweight
        'infisical': 192,       # Infisical secret storage - lightweight
        'monitoring': 1536,    # ClickHouse(1024) + SigNoz(512) - heavy
        'salon': 288,          # Backend(192) + Frontend(96) - reduced
        'treatment': 288,      # Backend(192) + Frontend(96) - reduced
        'staff': 288,          # Backend(192) + Frontend(96) - reduced
        'appointment': 576,    # 2x Backend(192) + 2x Frontend(96) - reduced
        'identity': 256,       # Backend(256)
        'elk': 1024,           # Elasticsearch(512) + Kibana(384) + others(128) - reduced
        'debezium': 448        # Enhanced: NATS Bridge(128) + Debezium Connect(256) + UI(64) - reduced
    }

    for service in memory_estimates.keys():
        if should_enable(service):
            enabled_services.append(service)
            estimated_memory += memory_estimates[service]

    print("\n📊 MAC M1 RESOURCE SUMMARY:")
    print("   🔧 Enabled services: " + str(len(enabled_services)))
    print("   💾 Estimated total memory: ~" + str(estimated_memory) + "MB")
    if estimated_memory > 4096:
        print("   ⚠️  WARNING: High memory usage detected! Consider disabling some services.")
    elif estimated_memory > 2048:
        print("   ⚡ MODERATE: Memory usage is moderate for Mac M1")
    else:
        print("   ✅ OPTIMAL: Memory usage is optimized for Mac M1")

    return enabled_services, estimated_memory

# =============================================================================
# INFRASTRUCTURE SERVICES
# =============================================================================



# Database Management Services (PostgreSQL, migrators, and messaging)
if should_enable('database-management'):
    print("🗃️  Loading database management services...")

    # Database management services (PostgreSQL, migrators)
    docker_compose('services/database-management/docker-compose.yml')

    # Messaging services (NATS, Kafka, Zookeeper, Redis)
    docker_compose('services/messaging/docker-compose.yml')

    # Configure PostgreSQL
    dc_resource('postgres',
        labels=['infra.tools'],
        resource_deps=['init-networks'],
        auto_init=True
    )

    # Configure messaging and cache resources
    dc_resource('redis',
        labels=['infra.messaging'],
        resource_deps=[],
        auto_init=False
    )

    dc_resource('nats',
        labels=['infra.messaging'],
        resource_deps=[],
        auto_init=True
    )

    dc_resource('kafka',
        labels=['cdc'],
        resource_deps=['zookeeper'],
        auto_init=False
    )

    dc_resource('zookeeper',
        labels=['cdc'],
        resource_deps=[],
        auto_init=False
    )

    # dc_resource('service-discovery-agent',
    #     labels=['infra.tools'],
    #     resource_deps=['nats'],
    #     auto_init=True
    # )

    # Database migration sidecars are now loaded conditionally in their respective service sections

if should_enable('verdaccio'):
    print("📦 Loading Verdaccio local registry...")

    # Verdaccio local npm registry
    docker_compose('docker-compose.verdaccio.yml')

    # Configure Verdaccio resource
    dc_resource('verdaccio',
        labels=['infra.tools', 'registry'],
        resource_deps=['init-networks'],
        auto_init=True
    )

    # Connect Verdaccio to backend network so services can access it
    local_resource('verdaccio-connect-network',
        cmd='docker network connect beauty_crm_backend beauty-crm-verdaccio 2>/dev/null || true',
        labels=['infra.tools', 'registry'],
        resource_deps=['verdaccio', 'init-networks'],
        auto_init=True,
        deps=[]
    )

if should_enable('infisical'):
    print("🔐 Loading Infisical secrets management...")

    # Infisical secrets management service
    docker_compose('docker-compose.infisical.yml')

    # Configure Infisical resources
    dc_resource('infisical-db',
        labels=['infra.tools', 'secrets'],
        resource_deps=['init-networks'],
        auto_init=True
    )

    dc_resource('infisical-redis',
        labels=['infra.tools', 'secrets'],
        resource_deps=['init-networks'],
        auto_init=True
    )

    dc_resource('infisical',
        labels=['infra.tools', 'secrets'],
        resource_deps=['init-networks', 'infisical-db', 'infisical-redis'],
        auto_init=True
    )

if should_enable('proxy'):
    print("🌐 Loading proxy services...")

    # Proxy services (Traefik, Dashy)
    docker_compose('services/proxy/docker-compose.yml')

    # Configure proxy resources
    dc_resource('traefik',
        labels=['infra.tools'],
        resource_deps=['init-networks'],
        auto_init=True
    )



    dc_resource('dashy',
        labels=['infra.tools'],
        resource_deps=['traefik'],
        auto_init=False
    )



if should_enable('monitoring'):
    print("📊 Loading monitoring services...")
    
    # APM/Monitoring services
    docker_compose('services/monitoring/docker-compose.yml')
    
    # Configure monitoring resources
    dc_resource('signoz-frontend',
        labels=['observability.apm'],
        resource_deps=[],
        auto_init=True
    )

    dc_resource('signoz-otel-collector',
        labels=['observability.apm'],
        resource_deps=[],
        auto_init=True
    )

    dc_resource('signoz-query-service',
        labels=['observability.apm'],
        resource_deps=[],
        auto_init=True
    )

    dc_resource('clickhouse',
        labels=['observability.storage'],
        resource_deps=[],
        auto_init=True
    )

    # Prometheus and Grafana (commented out - using SkyWalking instead)
    # dc_resource('prometheus',
    #     labels=['monitoring', 'metrics'],
    #     resource_deps=[],
    #     auto_init=True
    # )

    # dc_resource('grafana',
    #     labels=['monitoring', 'ui'],
    #     resource_deps=['prometheus'],
    #     auto_init=True
    # )

    dc_resource('elasticsearch',
        labels=['observability.storage'],
        resource_deps=[],
        auto_init=True
    )

    dc_resource('skywalking-oap',
        labels=['observability.apm'],
        resource_deps=['elasticsearch'],
        auto_init=True
    )

    dc_resource('skywalking-ui',
        labels=['observability.apm'],
        resource_deps=['skywalking-oap'],
        auto_init=True
    )

# Add Enhanced Debezium CDC services for event-driven architecture
if should_enable('debezium'):
    print("🔄 Loading Enhanced Debezium CDC services...")

    # Enhanced Debezium CDC services (simplified architecture without Kafka dependency)
    docker_compose('services/cdc/docker-compose.enhanced.yml')

    # Configure Enhanced Debezium resources
    # Note: NATS is managed in database-management services

    dc_resource('nats-http-bridge',
        labels=['cdc'],
        resource_deps=['nats'],
        auto_init=True
    )

    dc_resource('debezium-connect',
        labels=['cdc'],
        resource_deps=['kafka', 'postgres', 'nats-http-bridge'],
        auto_init=True
    )

    # dc_resource('debezium-ui',
    #     labels=['cdc'],
    #     resource_deps=['debezium-connect'],
    #     auto_init=False
    # )

    dc_resource('enhanced-connector-setup',
        labels=['cdc'],
        resource_deps=['debezium-connect', 'postgres', 'nats-http-bridge'],
        auto_init=False
    )

# =============================================================================
# APPLICATION SERVICES
# =============================================================================

if should_enable('salon'):
    print("💄 Loading salon services...")

    # Salon management services
    docker_compose('services/salon/docker-compose.app.yml')

    # Load salon migrator only when salon service is enabled
    if should_enable('database-management'):
        docker_compose('services/salon/docker-compose.migrator.yml')

        dc_resource('salon-db-migrator',
            labels=['app.salon'],
            resource_deps=['postgres'],
            auto_init=True
        )

    # Configure salon resources
    salon_deps = []
    if should_enable('database-management'):
        salon_deps.extend(['postgres'])  # PostgreSQL from database-management
        salon_deps.extend(['salon-db-migrator'])  # Migrator is loaded conditionally above
    if should_enable('verdaccio'):
        salon_deps.extend(['verdaccio'])  # Verdaccio local registry for internal packages

    dc_resource('salon-management-backend',
        labels=['app.salon'],
        resource_deps=salon_deps,
        auto_init=True
    )

    # Only load frontend if not in backends-only mode
    if not should_enable('backends-only'):
        dc_resource('salon-management-frontend',
            labels=['app.salon'],
            resource_deps=['salon-management-backend'],
            auto_init=True
        )

    # Salon API Gateway Proxies (DISABLED FOR DIRECT TRAEFIK ROUTING)
    # dc_resource('salon-frontend-proxy',
    #     labels=['infrastructure', 'api-gateway', 'salon'],
    #     resource_deps=['salon-management-frontend'],
    #     auto_init=True
    # )

    # dc_resource('salon-backend-proxy',
    #     labels=['infrastructure', 'api-gateway', 'salon'],
    #     resource_deps=['salon-management-backend'],
    #     auto_init=True
    # )

if should_enable('treatment'):
    print("🧴 Loading treatment services...")

    # Treatment management services
    docker_compose('services/treatment/docker-compose.app.yml')

    # Load treatment migrator only when treatment service is enabled
    if should_enable('database-management'):
        docker_compose('services/treatment/docker-compose.migrator.yml')

        dc_resource('treatment-db-migrator',
            labels=['app.treatment'],
            resource_deps=['postgres'],
            auto_init=True
        )

    # Configure treatment resources
    treatment_deps = []
    if should_enable('database-management'):
        treatment_deps.extend(['postgres'])  # PostgreSQL from database-management
        treatment_deps.extend(['treatment-db-migrator'])  # Migrator is loaded conditionally above

    dc_resource('treatment-management-backend',
        labels=['app.treatment'],
        resource_deps=treatment_deps,
        auto_init=True
    )

    # Only load frontend if not in backends-only mode
    if not should_enable('backends-only'):
        dc_resource('treatment-management-frontend',
            labels=['app.treatment'],
            resource_deps=['treatment-management-backend'],
            auto_init=True
        )

    # Treatment API Gateway Proxies (DISABLED FOR DIRECT TRAEFIK ROUTING)
    # dc_resource('treatment-frontend-proxy',
    #     labels=['infrastructure', 'api-gateway', 'treatment'],
    #     resource_deps=['treatment-management-frontend'],
    #     auto_init=True
    # )

    # dc_resource('treatment-backend-proxy',
    #     labels=['infrastructure', 'api-gateway', 'treatment'],
    #     resource_deps=['treatment-management-backend'],
    #     auto_init=True
    # )

if should_enable('staff'):
    print("🔧 Loading staff services...")

    # Staff management services
    docker_compose('services/staff/docker-compose.app.yml')

    # Load staff migrator only when staff service is enabled
    if should_enable('database-management'):
        docker_compose('services/staff/docker-compose.migrator.yml')

        dc_resource('staff-db-migrator',
            labels=['app.staff'],
            resource_deps=['postgres'],
            auto_init=True
        )

    # Configure staff resources
    staff_deps = []
    if should_enable('database-management'):
        staff_deps.extend(['postgres'])  # PostgreSQL from database-management
        staff_deps.extend(['staff-db-migrator'])  # Migrator is loaded conditionally above

    dc_resource('staff-management-backend',
        labels=['app.staff'],
        resource_deps=staff_deps,
        auto_init=True
    )

    # Only load frontend if not in backends-only mode
    if not should_enable('backends-only'):
        dc_resource('staff-management-frontend',
            labels=['app.staff'],
            resource_deps=['staff-management-backend'],
            auto_init=True
        )

if should_enable('appointment'):
    print("📅 Loading appointment services...")

    # Appointment services
    docker_compose('services/appointment/docker-compose.app.yml')
    docker_compose('services/appointment/docker-compose.planner.yml')

    # Load appointment migrators only when appointment service is enabled
    if should_enable('database-management'):
        docker_compose('services/appointment/docker-compose.migrator.yml')

        dc_resource('appointment-planner-db-migrator',
            labels=['app.appointment'],
            resource_deps=['postgres'],
            auto_init=True
        )

        dc_resource('appointment-management-db-migrator',
            labels=['app.appointment'],
            resource_deps=['postgres'],
            auto_init=True
        )

    # Build appointment services with live updates
    docker_build('appointment_appointment-management-backend',
        '.',
        dockerfile='services/appointment/appointment-management-backend/Dockerfile',
        target='production',
        live_update=[
            sync('services/appointment/appointment-management-backend/src', '/app/src'),
            run('bun install', trigger=['services/appointment/appointment-management-backend/package.json']),
            restart_container()
        ]
    )

    # Only build frontend images if not in backends-only mode
    if not should_enable('backends-only'):
        docker_build('appointment_appointment-management-frontend',
            '.',
            dockerfile='services/appointment/appointment-management-frontend/Dockerfile',
            live_update=[
                sync('services/appointment/appointment-management-frontend/src', '/app/src'),
                sync('services/appointment/appointment-management-frontend/public', '/app/public'),
                run('bun install', trigger=['services/appointment/appointment-management-frontend/package.json']),
                restart_container()
            ]
        )

    docker_build('appointment_appointment-planner-backend',
        '.',
        dockerfile='services/appointment/appointment-planner-backend/Dockerfile',
        target='production',
        live_update=[
            sync('services/appointment/appointment-planner-backend/src', '/app/src'),
            run('bun install', trigger=['services/appointment/appointment-planner-backend/package.json']),
            restart_container()
        ]
    )

    # Only build planner frontend images if not in backends-only mode
    if not should_enable('backends-only'):
        docker_build('appointment_appointment-planner-frontend',
            '.',
            dockerfile='services/appointment/appointment-planner-frontend/Dockerfile',
            target='production',
            # Note: For nginx-based frontend, live updates require rebuilding the dist folder
            # Consider using a development Dockerfile for live updates if needed
            live_update=[
                # Sync built assets (requires local build first)
                sync('services/appointment/appointment-planner-frontend/dist', '/usr/share/nginx/html'),
            ]
        )

    # Configure appointment resources
    appointment_deps = []
    if should_enable('database-management'):
        appointment_deps.extend(['postgres', 'nats'])  # PostgreSQL and NATS from database-management
        appointment_deps.extend(['appointment-planner-db-migrator', 'appointment-management-db-migrator'])  # Migrators are loaded conditionally above

    dc_resource('appointment-management-backend',
        labels=['app.appointment'],
        resource_deps=appointment_deps,
        auto_init=True
    )

    # Only load frontend if not in backends-only mode
    if not should_enable('backends-only'):
        dc_resource('appointment-management-frontend',
            labels=['app.appointment'],
            resource_deps=['appointment-management-backend'],
            auto_init=True
        )

    dc_resource('appointment-planner-backend',
        labels=['app.appointment'],
        resource_deps=appointment_deps,
        auto_init=True
    )

    # Only load frontend if not in backends-only mode
    if not should_enable('backends-only'):
        dc_resource('appointment-planner-frontend',
            labels=['app.appointment'],
            resource_deps=['appointment-planner-backend'],
            auto_init=True
        )


# =============================================================================
# API GATEWAY SERVICES (REMOVED)
# =============================================================================
# API Gateway functionality has been removed.
# Service proxies are now handled in their respective service Docker Compose files:
# - Salon proxies: services/salon/docker-compose.app.yml
# - Treatment proxies: services/treatment/docker-compose.app.yml
# - staff proxies: services/staff/docker-compose.app.yml
# - Appointment proxies: services/appointment/docker-compose.app.yml

# =============================================================================
# ADDITIONAL SERVICES
# =============================================================================

if should_enable('identity'):
    print("🔐 Loading identity services...")

    # Identity services
    docker_compose('services/public-identity/docker-compose.app.yml')

    # Load identity migrator only when identity service is enabled
    if should_enable('database-management'):
        docker_compose('services/public-identity/docker-compose.migrator.yml')

        dc_resource('identity-db-migrator',
            labels=['app.identity'],
            resource_deps=['postgres'],
            auto_init=True
        )

    # Configure identity resources
    identity_deps = []
    if should_enable('database-management'):
        identity_deps.extend(['postgres'])  # PostgreSQL from database-management
        identity_deps.extend(['identity-db-migrator'])  # Migrator is loaded conditionally above

    dc_resource('identity-backend',
        labels=['app.identity'],
        resource_deps=identity_deps,
        auto_init=True
    )

    dc_resource('identity-frontend',
        labels=['app.identity'],
        resource_deps=['identity-backend'],
        auto_init=True
    )

if should_enable('elk'):
    print("📊 Loading ELK stack...")

    # ELK stack services
    docker_compose('elk-compose.yml')

    # Configure ELK resources (adjust based on actual services in the file)
    dc_resource('elasticsearch',
        labels=['observability.elk'],
        resource_deps=[],
        auto_init=True
    )

    dc_resource('logstash',
        labels=['observability.elk', 'processor', 'logs'],
        resource_deps=['elasticsearch'],
        auto_init=True
    )

    dc_resource('kibana',
        labels=['observability.elk', 'frontend', 'dashboard'],
        resource_deps=['elasticsearch'],
        auto_init=True
    )

# Baserow services have been removed from orchestration
# if should_enable('baserow'):
#     print("🗄️  Loading Baserow data platform...")
#     # Baserow files have been removed from services/orchestration/
#     # To re-enable Baserow, move the docker-compose.baserow.yml file to an appropriate service category

# =============================================================================
# DEVELOPMENT HELPERS
# =============================================================================

# Local resource for running tests
# local_resource('run-tests',
#     cmd='bun test',
#     deps=['./'],
#     labels=['dev.quality'],
#     auto_init=False,
#     resource_deps=[]
# )

# Local resource for comprehensive linting (entire project)
local_resource('lint',
    cmd='bun biome lint --write .',
    deps=[
        'services/',
        'shared-platform-engineering/',
        'shared-product-engineering/',
        'shared-ddd-layers/',
        'biome.json',
        '.biomeignore'
    ],
    labels=['dev.quality'],
    auto_init=True
)

# Local resource for formatting (full project including shared libraries)
local_resource('format',
    cmd='bun run format',
    deps=[
        'services/',
        'shared-platform-engineering/',
        'shared-product-engineering/',
        'shared-ddd-layers/',
        'biome.json',
        '.biomeignore'
    ],
    labels=['dev.quality'],
    auto_init=True
)

# Local resource for building shared libraries
local_resource('build-shared',
    cmd='bun run build:shared',
    deps=[
        'shared-platform-engineering/',
        'shared-product-engineering/',
        'shared-ddd-layers/'
    ],
    ignore=[
        '**/*.test.ts',
        '**/*.test.tsx',
        '**/*.test.js',
        '**/*.test.jsx',
        '**/*.spec.ts',
        '**/*.spec.tsx',
        '**/*.spec.js',
        '**/*.spec.jsx',
        '**/test/**',
        '**/tests/**',
        '**/__tests__/**',
        '**/vitest.config.*',
        '**/jest.config.*',
        '**/playwright.config.*',
        '**/dist/**',
        '**/node_modules/**',
        '**/.tsbuildinfo',
        '**/*.d.ts',
        '**/*.d.ts.map',
        '**/*.js.map',
        '**/src/**/*.js',
        '**/tsup.config.bundled_*.mjs',
        '**/tsconfig.tsbuildinfo',
        '**/.lock-*.tmp',
        '**/bun.lock'
    ],
    labels=['dev.quality'],
    auto_init=True,
    resource_deps=[]
)





# REMOVED: quality-check resource (was causing infinite build loops)
# Use individual 'lint', 'format', and 'build-shared' resources instead
# These are already defined above and work properly without loops

# Local resource for Mac M1 monitoring and memory optimization
local_resource('mac-m1-monitor',
    cmd='echo "🔧 MAC M1 MEMORY OPTIMIZATION SUGGESTIONS:" && echo "• Current Docker memory limit: $(docker system info | grep -i "total memory" || echo "Unknown")" && echo "• Recommended: Set Docker Desktop memory to 6-8GB for Beauty CRM" && echo "• To reduce memory: Use fewer services simultaneously" && echo "• Heavy services: monitoring, elk (>512MB each), enhanced debezium (~448MB)" && echo "" && echo "📊 MAC M1 RESOURCE MONITORING" && docker stats --no-stream --format "table {{.Container}}\\t{{.CPUPerc}}\\t{{.MemUsage}}" $(docker ps --format "{{.Names}}" | grep -E "(beauty_crm|appointment|salon|staff|treatment|postgres|redis|nats|kafka|traefik)" | head -20) || echo "No containers running"',
    labels=['mac-m1'],
    auto_init=False
)

# =============================================================================
# CONFIGURATION SUMMARY WITH RESOURCE MONITORING
# =============================================================================

print("🚀 Tilt configuration loaded!")

# Print resource summary
enabled_services, estimated_memory = print_resource_summary()

print("\n📋 Service Status:")
for service in ['database-management', 'proxy', 'verdaccio', 'monitoring', 'salon', 'treatment', 'staff', 'appointment', 'backends-only', 'identity', 'elk', 'debezium']:
    if should_enable(service):
        print("   ✅ " + service)
    else:
        print("   ❌ " + service)

print("\n🔧 MAC M1 OPTIMIZED Usage Examples:")
print("   tilt up                                    # Minimal services (~1664MB): database-management (includes PostgreSQL + messaging), proxy, verdaccio")
print("   tilt up -- --appointment                   # Add appointment services (~2240MB)")
print("   tilt up -- --appointment --salon           # Add salon management (~2528MB)")
print("   tilt up -- --staff --treatment             # Enable staff and treatment (~2240MB)")
print("   tilt up -- --monitoring                    # Enable monitoring (~3200MB) - HEAVY!")
print("   tilt up -- --debezium                      # Enable Enhanced CDC services (~2112MB)")
print("   tilt up -- --elk                           # Enable ELK stack (~2688MB) - HEAVY!")
print("   tilt down                                  # Stop all services")
print("\n⚠️  MEMORY WARNINGS:")
print("   • Avoid enabling monitoring + elk + all apps simultaneously (>4GB)")
print("   • database-management now includes PostgreSQL, NATS, Kafka, Redis, Zookeeper")
print("   • For development, enable only needed services")
print("   • Use 'tilt trigger' commands for one-time operations")
print("\n🛠️  Development quality tools (auto-run on file changes):")
print("   format                                     # Auto-format code with Biome")
print("   lint                                       # Auto-lint services and shared libs")
print("   build-shared                               # Auto-build shared libraries")
print("\n🔍 Manual quality tools:")
print("   tilt trigger lint                      # Lint entire project")
print("   tilt trigger format                    # Format entire project")
print("   tilt trigger build-shared               # Build shared libraries")
# print("   tilt trigger run-tests                  # Run test suite")
print("\n🌐 Access points:")
print("   http://beauty-crm.localhost                # Main application")
print("   http://traefik.localhost                   # Traefik dashboard")
print("   http://dashboard.localhost                 # Dashy dashboard")
print("   http://localhost:4873                      # Verdaccio local npm registry")
print("   http://tilt.localhost                      # Tilt development UI")
print("   http://baserow.localhost                   # Baserow data platform")
print("   http://nats.localhost                      # NATS monitoring")
