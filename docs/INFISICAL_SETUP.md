# Infisical Secrets Management Setup

## Overview

Infisical is an open-source secrets management platform integrated into the Beauty CRM infrastructure to centralize and secure sensitive configuration data such as database credentials, API keys, JWT secrets, and other sensitive environment variables.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Beauty CRM Services                       │
│  ┌──────────┐  ┌──────────┐  ┌──────────┐  ┌──────────┐   │
│  │ Salon    │  │Treatment │  │  Staff   │  │Appointment│   │
│  │ Service  │  │ Service  │  │ Service  │  │  Service  │   │
│  └────┬─────┘  └────┬─────┘  └────┬─────┘  └────┬─────┘   │
│       │             │              │              │          │
│       └─────────────┴──────────────┴──────────────┘          │
│                          │                                    │
│                          ▼                                    │
│              ┌───────────────────────┐                       │
│              │  Infisical Service    │                       │
│              │  (Port 8080)          │                       │
│              └───────────┬───────────┘                       │
│                          │                                    │
│         ┌────────────────┼────────────────┐                 │
│         ▼                ▼                ▼                  │
│  ┌──────────┐    ┌──────────┐    ┌──────────┐              │
│  │PostgreSQL│    │  Redis   │    │ Traefik  │              │
│  │ Database │    │  Cache   │    │  Proxy   │              │
│  └──────────┘    └──────────┘    └──────────┘              │
└─────────────────────────────────────────────────────────────┘
```

## Quick Start

### 1. Generate Encryption Keys

Before starting Infisical, generate strong encryption keys:

```bash
# Generate encryption key (32 bytes hex)
openssl rand -hex 32

# Generate auth secret (base64)
openssl rand -base64 32
```

### 2. Configure Environment Variables

```bash
# Copy the example environment file
cp .env.infisical.example .env.infisical

# Edit the file and add your generated keys
nano .env.infisical
```

**Required Variables:**
- `INFISICAL_ENCRYPTION_KEY` - For encrypting secrets at rest
- `INFISICAL_AUTH_SECRET` - For JWT token signing
- `INFISICAL_DB_PASSWORD` - PostgreSQL database password

### 3. Start Infisical Services

```bash
# Start Infisical along with Verdaccio
docker compose -f docker-compose.verdaccio.yml up -d

# Check service status
docker compose -f docker-compose.verdaccio.yml ps

# View logs
docker compose -f docker-compose.verdaccio.yml logs -f infisical
```

### 4. Access Infisical UI

Open your browser and navigate to:
- **URL:** http://infisical.localhost
- **Traefik Dashboard:** http://traefik.localhost (to verify routing)

### 5. Initial Setup

1. **Create Admin Account**
   - Sign up with your email
   - Verify email (if SMTP configured)
   - Set up 2FA (recommended)

2. **Create Your First Project**
   - Click "New Project"
   - Name: `beauty-crm-production`
   - Description: "Production secrets for Beauty CRM"

3. **Create Environments**
   - Development
   - Staging
   - Production

## Migrating Existing Secrets

### Current Hardcoded Secrets

The following secrets are currently hardcoded in various Docker Compose files and should be migrated to Infisical:

#### Database Credentials
```yaml
# Current location: Multiple docker-compose.app.yml files
DATABASE_URL: ********************************************************************/beauty_crm_*
```

#### JWT Secrets
```yaml
# Current location: services/api-gateway/.env.example
JWT_SECRET: your-super-secret-jwt-key-here
```

#### AI Model API Keys
```yaml
# Current location: Multiple .env.example files
ANTHROPIC_API_KEY
OPENAI_API_KEY
GOOGLE_API_KEY
MISTRAL_API_KEY
```

### Migration Steps

1. **Add Secrets to Infisical**
   ```bash
   # Using Infisical CLI
   infisical secrets set DB_PASSWORD beauty_crm_password --env production
   infisical secrets set JWT_SECRET your-jwt-secret --env production
   ```

2. **Update Docker Compose Files**
   ```yaml
   # Before
   environment:
     - DATABASE_URL=***************************************************/beauty_crm
   
   # After (using Infisical)
   environment:
     - DATABASE_URL=postgresql://beauty_crm:${DB_PASSWORD}@db:5432/beauty_crm
   ```

3. **Inject Secrets at Runtime**
   ```bash
   # Using Infisical CLI to run services with secrets
   infisical run --env production -- docker compose up
   ```

## Integration Methods

### Method 1: Infisical CLI (Recommended)

```bash
# Install Infisical CLI
brew install infisical/get-cli/infisical

# Login
infisical login

# Run commands with secrets injected
infisical run --env production -- bun run dev
```

### Method 2: Infisical SDK (Node.js)

```typescript
import { InfisicalClient } from "@infisical/sdk";

const client = new InfisicalClient({
  siteUrl: "http://infisical.localhost",
});

await client.auth().universalAuth.login({
  clientId: process.env.INFISICAL_CLIENT_ID,
  clientSecret: process.env.INFISICAL_CLIENT_SECRET,
});

const secrets = await client.secrets().listSecrets({
  environment: "production",
  projectId: "your-project-id",
});
```

### Method 3: Infisical Agent (Kubernetes/Docker)

For containerized environments, use the Infisical Agent to automatically inject secrets.

## Security Best Practices

1. **Encryption Keys**
   - Never commit encryption keys to version control
   - Store keys in a secure location (password manager, hardware security module)
   - Rotate keys periodically

2. **Access Control**
   - Use role-based access control (RBAC)
   - Implement least privilege principle
   - Enable audit logging

3. **Network Security**
   - Infisical runs on `beauty_crm_traefik-private` network
   - Only accessible via Traefik proxy
   - Consider adding HTTPS/TLS in production

4. **Backup & Recovery**
   - Regular database backups
   - Document recovery procedures
   - Test restore process

## Monitoring & Maintenance

### Health Checks

```bash
# Check Infisical health
curl http://infisical.localhost/api/status

# Check database connection
docker exec beauty-crm-infisical-db pg_isready -U infisical

# Check Redis connection
docker exec beauty-crm-infisical-redis redis-cli ping
```

### Resource Usage

The Infisical stack is configured with resource limits:

| Service | Memory Limit | CPU Limit | Memory Reservation |
|---------|--------------|-----------|-------------------|
| Infisical | 256MB | 0.25 | 128MB |
| PostgreSQL | 384MB | 0.25 | 192MB |
| Redis | 192MB | 0.15 | 96MB |

### Logs

```bash
# View Infisical logs
docker compose -f docker-compose.verdaccio.yml logs -f infisical

# View database logs
docker compose -f docker-compose.verdaccio.yml logs -f infisical-db

# View Redis logs
docker compose -f docker-compose.verdaccio.yml logs -f infisical-redis
```

## Troubleshooting

### Issue: Cannot Access Infisical UI

**Solution:**
```bash
# Check if Traefik is running
docker ps | grep traefik

# Check Traefik routing
curl -H "Host: infisical.localhost" http://localhost

# Check Infisical container logs
docker logs beauty-crm-infisical
```

### Issue: Database Connection Failed

**Solution:**
```bash
# Check database health
docker exec beauty-crm-infisical-db pg_isready -U infisical

# Verify database credentials
docker exec beauty-crm-infisical-db psql -U infisical -d infisical -c "SELECT 1"

# Check connection string
docker exec beauty-crm-infisical env | grep DB_CONNECTION_URI
```

### Issue: Redis Connection Failed

**Solution:**
```bash
# Check Redis health
docker exec beauty-crm-infisical-redis redis-cli ping

# Check Redis logs
docker logs beauty-crm-infisical-redis

# Verify Redis URL
docker exec beauty-crm-infisical env | grep REDIS_URL
```

### Issue: Encryption Key Error

**Solution:**
```bash
# Verify encryption key is set
docker exec beauty-crm-infisical env | grep ENCRYPTION_KEY

# Regenerate encryption key
openssl rand -hex 32

# Update .env.infisical and restart
docker compose -f docker-compose.verdaccio.yml restart infisical
```

## Advanced Configuration

### SMTP Email Notifications

To enable email notifications for user invites, password resets, etc.:

```bash
# Edit .env.infisical
INFISICAL_SMTP_HOST=smtp.gmail.com
INFISICAL_SMTP_PORT=587
INFISICAL_SMTP_SECURE=false
INFISICAL_SMTP_USERNAME=<EMAIL>
INFISICAL_SMTP_PASSWORD=your-app-password
INFISICAL_SMTP_FROM=<EMAIL>
```

### Custom Domain & HTTPS

For production deployments with custom domains:

```yaml
# docker-compose.verdaccio.yml
labels:
  - "traefik.http.routers.infisical.rule=Host(`secrets.beauty-crm.com`)"
  - "traefik.http.routers.infisical.tls=true"
  - "traefik.http.routers.infisical.tls.certresolver=letsencrypt"
```

### Backup Strategy

```bash
# Backup PostgreSQL database
docker exec beauty-crm-infisical-db pg_dump -U infisical infisical > infisical_backup_$(date +%Y%m%d).sql

# Backup Redis data
docker exec beauty-crm-infisical-redis redis-cli SAVE
docker cp beauty-crm-infisical-redis:/data/dump.rdb ./infisical_redis_backup_$(date +%Y%m%d).rdb

# Restore PostgreSQL database
docker exec -i beauty-crm-infisical-db psql -U infisical infisical < infisical_backup_20260107.sql
```

## Integration with Beauty CRM Services

### Example: Salon Service Integration

```typescript
// services/salon/salon-management-backend/src/config/secrets.ts
import { InfisicalClient } from "@infisical/sdk";

export async function loadSecrets() {
  const client = new InfisicalClient({
    siteUrl: "http://infisical.localhost",
  });

  await client.auth().universalAuth.login({
    clientId: process.env.INFISICAL_CLIENT_ID!,
    clientSecret: process.env.INFISICAL_CLIENT_SECRET!,
  });

  const secrets = await client.secrets().listSecrets({
    environment: process.env.NODE_ENV || "development",
    projectId: process.env.INFISICAL_PROJECT_ID!,
  });

  return secrets.reduce((acc, secret) => {
    acc[secret.secretKey] = secret.secretValue;
    return acc;
  }, {} as Record<string, string>);
}
```

### Example: Docker Compose Integration

```yaml
# services/salon/docker-compose.app.yml
services:
  salon-backend:
    environment:
      - INFISICAL_CLIENT_ID=${INFISICAL_CLIENT_ID}
      - INFISICAL_CLIENT_SECRET=${INFISICAL_CLIENT_SECRET}
      - INFISICAL_PROJECT_ID=${INFISICAL_PROJECT_ID}
    networks:
      - traefik-private  # Access to Infisical
```

## References

- [Infisical Official Documentation](https://infisical.com/docs)
- [Infisical Self-Hosting Guide](https://infisical.com/docs/self-hosting/overview)
- [Infisical Node.js SDK](https://infisical.com/docs/sdks/languages/node)
- [Infisical CLI](https://infisical.com/docs/cli/overview)

## Support

For issues or questions:
1. Check the [Troubleshooting](#troubleshooting) section
2. Review Infisical logs: `docker logs beauty-crm-infisical`
3. Consult [Infisical Community](https://infisical.com/slack)
4. Open an issue in the Beauty CRM repository


