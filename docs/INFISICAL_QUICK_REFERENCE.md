# Infisical Quick Reference Guide

## Common Commands

### Service Management

```bash
# Start Infisical services
docker compose -f docker-compose.verdaccio.yml up -d infisical infisical-db infisical-redis

# Stop Infisical services
docker compose -f docker-compose.verdaccio.yml stop infisical infisical-db infisical-redis

# Restart Infisical
docker compose -f docker-compose.verdaccio.yml restart infisical

# View logs
docker compose -f docker-compose.verdaccio.yml logs -f infisical

# Check status
docker compose -f docker-compose.verdaccio.yml ps
```

### Health Checks

```bash
# Check Infisical API
curl http://infisical.localhost/api/status

# Check database
docker exec beauty-crm-infisical-db pg_isready -U infisical

# Check Redis
docker exec beauty-crm-infisical-redis redis-cli ping

# Check all services
docker compose -f docker-compose.verdaccio.yml ps
```

### Infisical CLI

```bash
# Install CLI (macOS)
brew install infisical/get-cli/infisical

# Install CLI (Linux)
curl -1sLf 'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.deb.sh' | sudo -E bash
sudo apt-get update && sudo apt-get install -y infisical

# Login
infisical login

# Initialize project
infisical init

# List secrets
infisical secrets

# Set a secret
infisical secrets set API_KEY your-api-key --env production

# Get a secret
infisical secrets get API_KEY --env production

# Delete a secret
infisical secrets delete API_KEY --env production

# Run command with secrets injected
infisical run --env production -- bun run dev

# Export secrets to .env file
infisical export --env production > .env
```

## Secret Organization

### Recommended Project Structure

```
beauty-crm-production/
├── development/
│   ├── DB_PASSWORD
│   ├── JWT_SECRET
│   ├── ANTHROPIC_API_KEY
│   └── ...
├── staging/
│   ├── DB_PASSWORD
│   ├── JWT_SECRET
│   ├── ANTHROPIC_API_KEY
│   └── ...
└── production/
    ├── DB_PASSWORD
    ├── JWT_SECRET
    ├── ANTHROPIC_API_KEY
    └── ...
```

### Secret Naming Conventions

| Category | Prefix | Example |
|----------|--------|---------|
| Database | `DB_` | `DB_PASSWORD`, `DB_USER` |
| Authentication | `AUTH_` | `AUTH_JWT_SECRET`, `AUTH_SESSION_SECRET` |
| API Keys | `API_` | `API_ANTHROPIC_KEY`, `API_OPENAI_KEY` |
| SMTP | `SMTP_` | `SMTP_HOST`, `SMTP_PASSWORD` |
| Service URLs | `URL_` | `URL_REDIS`, `URL_NATS` |
| Feature Flags | `FEATURE_` | `FEATURE_AI_ENABLED` |

## Integration Patterns

### Pattern 1: Environment Variables (Simple)

```bash
# .env file
INFISICAL_CLIENT_ID=your-client-id
INFISICAL_CLIENT_SECRET=your-client-secret
INFISICAL_PROJECT_ID=your-project-id

# Run with Infisical CLI
infisical run --env production -- bun run start
```

### Pattern 2: SDK Integration (Advanced)

```typescript
// src/config/secrets.ts
import { InfisicalClient } from "@infisical/sdk";

const client = new InfisicalClient({
  siteUrl: process.env.INFISICAL_URL || "http://infisical.localhost",
});

await client.auth().universalAuth.login({
  clientId: process.env.INFISICAL_CLIENT_ID!,
  clientSecret: process.env.INFISICAL_CLIENT_SECRET!,
});

export async function getSecret(key: string): Promise<string> {
  const secret = await client.secrets().getSecret({
    environment: process.env.NODE_ENV || "development",
    projectId: process.env.INFISICAL_PROJECT_ID!,
    secretName: key,
  });
  return secret.secretValue;
}
```

### Pattern 3: Docker Compose Integration

```yaml
# docker-compose.app.yml
services:
  backend:
    environment:
      - INFISICAL_CLIENT_ID=${INFISICAL_CLIENT_ID}
      - INFISICAL_CLIENT_SECRET=${INFISICAL_CLIENT_SECRET}
      - INFISICAL_PROJECT_ID=${INFISICAL_PROJECT_ID}
    command: >
      sh -c "
        npm install -g @infisical/cli &&
        infisical run --env production -- bun run start
      "
```

## Secrets to Migrate

### Priority 1: Critical Secrets

- [ ] `DB_PASSWORD` - Database credentials
- [ ] `JWT_SECRET` - Authentication tokens
- [ ] `ENCRYPTION_KEY` - Data encryption
- [ ] `AUTH_SECRET` - Session management

### Priority 2: API Keys

- [ ] `ANTHROPIC_API_KEY`
- [ ] `OPENAI_API_KEY`
- [ ] `GOOGLE_API_KEY`
- [ ] `MISTRAL_API_KEY`
- [ ] `XAI_API_KEY`

### Priority 3: Service Credentials

- [ ] `SMTP_PASSWORD`
- [ ] `REDIS_PASSWORD`
- [ ] `GITHUB_TOKEN`
- [ ] `SLACK_BOT_TOKEN`

## Troubleshooting Checklist

- [ ] Infisical service is running: `docker ps | grep infisical`
- [ ] Database is healthy: `docker exec beauty-crm-infisical-db pg_isready`
- [ ] Redis is healthy: `docker exec beauty-crm-infisical-redis redis-cli ping`
- [ ] Traefik routing works: `curl -H "Host: infisical.localhost" http://localhost`
- [ ] Environment variables are set: `docker exec beauty-crm-infisical env | grep ENCRYPTION_KEY`
- [ ] Logs show no errors: `docker logs beauty-crm-infisical`

## Security Checklist

- [ ] Generated strong encryption key (32 bytes)
- [ ] Generated strong auth secret (base64)
- [ ] Changed default database password
- [ ] Added `.env.infisical` to `.gitignore`
- [ ] Enabled 2FA for admin account
- [ ] Configured RBAC for team members
- [ ] Set up audit logging
- [ ] Configured backup strategy
- [ ] Tested secret rotation procedure
- [ ] Documented recovery process

## Quick Links

- **Infisical UI:** http://infisical.localhost
- **Traefik Dashboard:** http://traefik.localhost
- **Verdaccio:** http://verdaccio.localhost
- **Documentation:** [INFISICAL_SETUP.md](./INFISICAL_SETUP.md)

