# Prisma v7.2.0 Migration - Complete Todo List

## Overview
Migrate all services and packages from Prisma v6.x to v7.2.0 across the entire Beauty CRM monorepo.

**Current Version:** v6.9.0 - v6.18.0 (mixed)  
**Target Version:** v7.2.0  
**Migration Guide:** https://pris.ly/d/major-version-upgrade

---

## Phase 1: Pre-Migration Preparation

### 1.1 Audit Current Prisma Usage
- [ ] List all packages using Prisma (completed below)
- [ ] Document current versions per package
- [ ] Identify all schema.prisma files
- [ ] Review breaking changes in v7.x release notes
- [ ] Check for deprecated features in current code

### 1.2 Backup & Safety
- [ ] Create git branch: `feat/prisma-v7-migration`
- [ ] Backup all database schemas
- [ ] Document current migration states
- [ ] Ensure all tests pass on current version
- [ ] Tag current state: `pre-prisma-v7-migration`

---

## Phase 2: Update Package Dependencies

### 2.1 Core Platform Packages

#### platform-db-client
- [ ] Update `@prisma/client` from ^6.9.0 to ^7.2.0
- [ ] Update `prisma` from ^6.9.0 to ^7.2.0
- [ ] Run `bunx prisma generate`
- [ ] Test build: `bun run build`
- [ ] Publish to Verdaccio

#### infrastructure (DDD layer)
- [ ] Update `@prisma/client` from ^6.18.0 to ^7.2.0
- [ ] Run `bunx prisma generate`
- [ ] Test build: `bun run build`
- [ ] Publish to Verdaccio

### 2.2 Service Backends

#### salon-management-backend
- [ ] Update `@prisma/client` from ^6.9.0 to ^7.2.0
- [ ] Update `prisma` from ^6.9.0 to ^7.2.0
- [ ] Update schema.prisma generator config if needed
- [ ] Run `bunx prisma generate`
- [ ] Run `bunx prisma migrate dev` (test)
- [ ] Test build and Docker image
- [ ] Verify health endpoint

#### staff-management-backend
- [ ] Update `@prisma/client` from ^6.18.0 to ^7.2.0
- [ ] Update `prisma` from ^6.18.0 to ^7.2.0
- [ ] Update schema.prisma generator config if needed
- [ ] Run `bunx prisma generate`
- [ ] Run `bunx prisma migrate dev` (test)
- [ ] Test build and Docker image

#### treatment-management-backend
- [ ] Update `@prisma/client` from ^6.18.0 to ^7.2.0
- [ ] Update `prisma` from ^6.19.1 to ^7.2.0
- [ ] Update schema.prisma generator config if needed
- [ ] Run `bunx prisma generate`
- [ ] Run `bunx prisma migrate dev` (test)
- [ ] Test build and Docker image

#### appointment-management-backend
- [ ] Update `@prisma/client` from ^6.18.0 to ^7.2.0
- [ ] Update `prisma` from ^6.18.0 to ^7.2.0
- [ ] Update schema.prisma generator config if needed
- [ ] Run `bunx prisma generate`
- [ ] Run `bunx prisma migrate dev` (test)
- [ ] Test build and Docker image

#### appointment-planner-backend
- [ ] Update `@prisma/client` to ^7.2.0
- [ ] Update `prisma` to ^7.2.0
- [ ] Update schema.prisma generator config if needed
- [ ] Run `bunx prisma generate`
- [ ] Run `bunx prisma migrate dev` (test)
- [ ] Test build and Docker image

#### public-identity-management-backend
- [ ] Update `@prisma/client` to ^7.2.0
- [ ] Update `prisma` to ^7.2.0
- [ ] Update schema.prisma generator config if needed
- [ ] Run `bunx prisma generate`
- [ ] Run `bunx prisma migrate dev` (test)
- [ ] Test build and Docker image

### 2.3 Other Services

#### neural-mcp
- [ ] Update `@prisma/client` from ^6.18.0 to ^7.2.0
- [ ] Update `prisma` from ^6.18.0 to ^7.2.0
- [ ] Run `bunx prisma generate`
- [ ] Test build

---

## Phase 3: Schema & Migration Updates

### 3.1 Review Schema Files
- [ ] Check all schema.prisma files for deprecated syntax
- [ ] Update generator blocks if needed
- [ ] Review binaryTargets configuration
- [ ] Check for preview features that are now stable

### 3.2 Migration Files
- [ ] Review existing migrations for compatibility
- [ ] Test migration replay on clean database
- [ ] Document any migration issues
- [ ] Create new migrations if schema changes needed

---

## Phase 4: Code Updates

### 4.1 Breaking Changes
- [ ] Review Prisma v7 breaking changes documentation
- [ ] Update PrismaClient instantiation if changed
- [ ] Update query syntax for deprecated methods
- [ ] Fix TypeScript type issues
- [ ] Update error handling for new error types

### 4.2 Deprecated Features
- [ ] Replace deprecated `@prisma/client` imports
- [ ] Update middleware usage if changed
- [ ] Review transaction API changes
- [ ] Check for deprecated query methods

---

## Phase 5: Testing

### 5.1 Unit Tests
- [ ] Run all unit tests across services
- [ ] Fix failing tests
- [ ] Update test mocks if needed

### 5.2 Integration Tests
- [ ] Test database connections
- [ ] Test CRUD operations per service
- [ ] Test migrations on test databases
- [ ] Run Cucumber tests (salon service)

### 5.3 End-to-End Tests
- [ ] Test salon creation and retrieval
- [ ] Test staff management operations
- [ ] Test treatment catalog operations
- [ ] Test appointment booking flow
- [ ] Test cross-service communication

---

## Phase 6: Docker & Infrastructure

### 6.1 Dockerfile Updates
- [ ] Update Prisma generate commands in Dockerfiles
- [ ] Test Docker builds for all services
- [ ] Verify Prisma client generation in containers
- [ ] Test multi-stage builds

### 6.2 Tilt Configuration
- [ ] Update Tilt resources if needed
- [ ] Test full stack startup with Tilt
- [ ] Verify all services healthy
- [ ] Check database connectivity

---

## Phase 7: Documentation & Cleanup

### 7.1 Update Documentation
- [ ] Update README files with new Prisma version
- [ ] Document migration steps
- [ ] Update developer setup guides
- [ ] Add troubleshooting section

### 7.2 Template Updates
- [ ] Update package templates with v7.2.0
- [ ] Update backend-package.template.json
- [ ] Update any generator scripts

### 7.3 Cleanup
- [ ] Remove old Prisma artifacts
- [ ] Clean up node_modules
- [ ] Regenerate lock files
- [ ] Remove deprecated code

---

## Phase 8: Deployment

### 8.1 Staging Deployment
- [ ] Deploy to staging environment
- [ ] Run smoke tests
- [ ] Monitor for errors
- [ ] Verify database migrations

### 8.2 Production Preparation
- [ ] Create deployment runbook
- [ ] Plan rollback strategy
- [ ] Schedule maintenance window
- [ ] Notify team

---

## Packages Requiring Updates

### Services with Prisma
1. ✅ **salon-management-backend** - @prisma/client: ^6.9.0, prisma: ^6.9.0
2. ✅ **staff-management-backend** - @prisma/client: ^6.18.0, prisma: ^6.18.0
3. ✅ **treatment-management-backend** - @prisma/client: ^6.18.0, prisma: ^6.19.1
4. ✅ **appointment-management-backend** - @prisma/client: ^6.18.0, prisma: ^6.18.0
5. ✅ **appointment-planner-backend** - (needs version check)
6. ✅ **public-identity-management-backend** - (needs version check)
7. ✅ **platform-db-client** - @prisma/client: ^6.9.0, prisma: ^6.9.0
8. ✅ **infrastructure** (DDD) - @prisma/client: ^6.18.0
9. ✅ **neural-mcp** - @prisma/client: ^6.18.0, prisma: ^6.18.0

### Schema Files Found
- services/salon/salon-management-backend/prisma/schema.prisma
- services/staff/staff-management-backend/prisma/schema.prisma
- services/treatment/treatment-management-backend/prisma/schema.prisma
- services/appointment/appointment-management-backend/prisma/schema.prisma
- services/appointment/appointment-planner-backend/prisma/schema.prisma
- services/public-identity/public-identity-management-backend/prisma/schema.prisma
- shared-platform-engineering/platform-db-client/prisma/schema.prisma
- shared-platform-engineering/neural-mcp/prisma/schema.prisma

---

## Quick Commands

### Update All Prisma Packages
```bash
# Update platform-db-client
cd shared-platform-engineering/platform-db-client
bun add @prisma/client@^7.2.0 -D prisma@^7.2.0

# Update each service
for service in salon staff treatment appointment; do
  cd services/$service/${service}-management-backend
  bun add @prisma/client@^7.2.0 -D prisma@^7.2.0
  cd ../../..
done
```

### Regenerate All Prisma Clients
```bash
# Find and regenerate all
find . -name "schema.prisma" -not -path "*/node_modules/*" -exec dirname {} \; | while read dir; do
  cd "$dir/.."
  echo "Generating Prisma client in $PWD"
  bunx prisma generate
  cd -
done
```

### Test All Builds
```bash
# Test platform package
cd shared-platform-engineering/platform-db-client && bun run build && cd ../..

# Test services
for service in salon staff treatment appointment; do
  cd services/$service/${service}-management-backend
  bun run build
  cd ../../..
done
```

---

## Notes & Considerations

1. **Breaking Changes**: Review https://github.com/prisma/prisma/releases/tag/7.0.0
2. **Binary Targets**: May need updates for ARM64/M1 Macs
3. **TypeScript**: Ensure TypeScript version compatibility
4. **Lock Files**: Regenerate bun.lockb after updates
5. **Verdaccio**: Republish platform packages after updates
6. **Docker**: Test Prisma generation in Alpine Linux containers
7. **Migrations**: Test on clean database before production

---

## Success Criteria

- [ ] All packages updated to Prisma v7.2.0
- [ ] All builds passing
- [ ] All tests passing
- [ ] Docker images building successfully
- [ ] Tilt stack running healthy
- [ ] No TypeScript errors
- [ ] Database migrations working
- [ ] API endpoints responding correctly
- [ ] Documentation updated

