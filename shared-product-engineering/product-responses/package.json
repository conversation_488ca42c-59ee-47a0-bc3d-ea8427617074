{"author": "Beauty CRM Platform Team", "description": "", "devDependencies": {"rimraf": "^6.1.2", "typescript": "^5.9.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/product-responses", "publishConfig": {"access": "public"}, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for product-responses' && exit 0"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}