{"author": "Beauty CRM Platform Team", "dependencies": {"@beauty-crm/platform-environment-names": "^1.0.0", "@beauty-crm/platform-logger": "^1.0.3", "@beauty-crm/platform-utilities": "^1.0.0"}, "description": "Shared kernel for orchestrator and sagas for Beauty CRM", "devDependencies": {"rimraf": "^6.1.2", "typescript": "^5.9.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/product-kernel", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for product-kernel' && exit 0"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}