{"name": "sdlc-vscode-agent", "displayName": "SDLC Agent - AI Code Assistant", "description": "AI-powered code assistant for SDLC tasks - analyze, fix, and improve your code", "version": "1.0.0", "publisher": "beauty-crm", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["ai", "assistant", "code-analysis", "bug-fixing", "langchain", "gemini"], "activationEvents": ["*"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "sdlc-agent.analyze", "title": "Analyze Code", "category": "SDLC Agent"}, {"command": "sdlc-agent.fix", "title": "Fix Code Issues", "category": "SDLC Agent"}, {"command": "sdlc-agent.chat", "title": "Chat with Agent", "category": "SDLC Agent"}, {"command": "sdlc-agent.explain", "title": "Explain Code", "category": "SDLC Agent"}, {"command": "sdlc-agent.openChat", "title": "Open SDLC Agent Chat", "category": "SDLC Agent"}], "menus": {"editor/context": [{"when": "editorHasSelection", "command": "sdlc-agent.analyze", "group": "sdlc-agent@1"}, {"when": "editorHasSelection", "command": "sdlc-agent.fix", "group": "sdlc-agent@2"}, {"when": "editorHasSelection", "command": "sdlc-agent.explain", "group": "sdlc-agent@3"}], "explorer/context": [{"when": "resourceExtname == .js || resourceExtname == .ts || resourceExtname == .py", "command": "sdlc-agent.analyze", "group": "sdlc-agent@1"}]}, "keybindings": [{"command": "sdlc-agent.chat", "key": "ctrl+shift+a", "mac": "cmd+shift+a"}, {"command": "sdlc-agent.fix", "key": "ctrl+shift+f", "mac": "cmd+shift+f"}], "views": {"explorer": [{"id": "sdlc-agent-chat", "name": "SDLC Agent", "when": "sdlc-agent:enabled"}]}, "configuration": {"title": "SDLC Agent", "properties": {"sdlc-agent.apiKey": {"type": "string", "default": "", "description": "Google API Key for Gemini", "scope": "application"}, "sdlc-agent.agentPath": {"type": "string", "default": "../shared-product-engineering/sdlc-langchain-agent", "description": "Path to SDLC LangChain Agent", "scope": "workspace"}, "sdlc-agent.autoAnalyze": {"type": "boolean", "default": false, "description": "Automatically analyze files on save", "scope": "workspace"}, "sdlc-agent.debugMode": {"type": "boolean", "default": false, "description": "Enable debug mode for verbose logging", "scope": "workspace"}}}}, "scripts": {"vscode:prepublish": "echo 'Skipping compilation for now'", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.107.0", "@types/node": "^25.0.3", "typescript": "^5.9.3"}, "dependencies": {}}