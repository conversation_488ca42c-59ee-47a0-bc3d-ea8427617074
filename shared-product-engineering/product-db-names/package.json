{"author": "Beauty CRM Platform Team", "description": "Shared database schemas/tables/databases for Beauty CRM", "devDependencies": {"rimraf": "^6.1.2", "typescript": "^5.9.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/product-db-names", "publishConfig": {"access": "public"}, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for product-db-names' && exit 0"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}