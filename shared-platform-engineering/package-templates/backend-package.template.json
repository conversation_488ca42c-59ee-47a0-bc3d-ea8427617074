{"dependencies": {"@hono/node-server": "^1.14.3", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^7.2.0", "axios": "^1.7.9", "hono": "^4.7.11", "nats": "^2.29.3", "uuid": "^11.1.0", "zod": "^3.25.51"}, "description": "Backend service for {SERVICE_NAME} management in Beauty CRM", "devDependencies": {"@types/bun": "latest", "@types/node": "^22.15.29", "@types/uuid": "^10.0.0", "esbuild": "^0.25.5", "prisma": "^7.2.0", "tsx": "^4.19.2", "typescript": "^5.8.3", "vitest": "^3.2.1"}, "engines": {"node": ">=20.0.0"}, "keywords": ["beauty-crm", "backend", "api", "express", "typescript"], "main": "dist/main.js", "name": "{SERVICE_NAME}-backend", "peerDependencies": {"typescript": "^5"}, "scripts": {"build": "tsc && esbuild src/main.ts --bundle --platform=node --target=node20 --outfile=dist/main.js --external:@prisma/client", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "tsx watch src/main.ts", "format": "biome format --write --fix .", "start": "node dist/main.js", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "1.0.0"}