import type { LucideIcon } from 'lucide-react';
import { Calendar, Home, Settings, Users } from 'lucide-react';
import type { FC, MouseEvent, ReactNode } from 'react';
import * as React from 'react';
import { cn } from '@/lib/utils';

export interface SidebarItemProps {
  icon: LucideIcon | FC<{ className?: string }>;
  label: string;
  href: string;
  isActive?: boolean;
  title?: string;
  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;
  className?: string;
}

export interface SidebarSectionProps {
  title?: string;
  children: ReactNode;
  className?: string;
}

export interface SidebarProps {
  children: ReactNode;
  className?: string;
  collapsed?: boolean;
}

/**
 * Elegant icon wrapper with hover and click animations
 */
const SidebarItemIcon: FC<{
  icon: LucideIcon | FC<{ className?: string }>;
  isActive: boolean;
}> = ({ icon: Icon, isActive }) => {
  const [isPressed, setIsPressed] = React.useState(false);

  return (
    <button
      type="button"
      aria-pressed={isPressed}
      className={cn(
        'relative flex items-center justify-center',
        'transition-all duration-300 ease-out',
        'mr-3 p-0 border-0 bg-transparent',
        'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 rounded-full',
        'cursor-pointer',
      )}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
    >
      {/* Icon glow effect on active */}
      {isActive && (
        <div className="absolute inset-0 bg-primary/20 rounded-full blur-md animate-pulse" />
      )}

      {/* Icon container with animations */}
      <div
        className={cn(
          'relative flex items-center justify-center',
          'transition-all duration-300 ease-out',
          'group-hover:scale-110',
          'group-hover:rotate-6',
          isPressed && 'scale-95 rotate-0',
          isActive && 'scale-105',
        )}
      >
        <Icon
          className={cn(
            'h-5 w-5 flex-shrink-0',
            'transition-all duration-300 ease-out',
            isActive && 'drop-shadow-sm',
          )}
          aria-hidden="true"
        />
      </div>

      {/* Ripple effect on click */}
      {isPressed && (
        <div className="absolute inset-0 bg-primary/30 rounded-full animate-ping" />
      )}
    </button>
  );
};

export const SidebarItem = ({
  icon: Icon,
  label,
  href,
  isActive = false,
  title,
  onClick,
  className,
}: SidebarItemProps) => {
  return (
    <a
      href={href}
      onClick={onClick}
      title={title || label}
      className={cn(
        'group relative flex items-center rounded-lg px-3 py-2.5 text-sm font-medium',
        'transition-all duration-300 ease-out',
        'overflow-hidden',
        isActive
          ? 'bg-primary/10 text-primary shadow-sm ring-1 ring-primary/20'
          : 'text-muted-foreground hover:bg-muted hover:text-foreground hover:shadow-sm',
        'active:scale-[0.98]',
        className,
      )}
      data-discover="true"
    >
      {/* Active indicator bar */}
      {isActive && (
        <div
          className={cn(
            'absolute inset-y-0 left-0 w-1 bg-primary rounded-r-full',
            'shadow-lg shadow-primary/50',
            'animate-in slide-in-from-left duration-300',
          )}
        />
      )}

      {/* Icon with animations */}
      <SidebarItemIcon icon={Icon} isActive={isActive} />

      {/* Label with smooth transitions */}
      <span
        className={cn(
          'truncate transition-all duration-200',
          'group-hover:translate-x-0.5',
          isActive && 'font-semibold',
        )}
      >
        {label}
      </span>

      {/* Hover background gradient */}
      <div
        className={cn(
          'absolute inset-0 -z-10 opacity-0',
          'bg-gradient-to-r from-primary/5 to-transparent',
          'transition-opacity duration-300',
          'group-hover:opacity-100',
        )}
      />
    </a>
  );
};

export const SidebarSection = ({
  title,
  children,
  className,
}: SidebarSectionProps) => {
  return (
    <div className={cn('space-y-1', className)}>
      {title && (
        <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
          {title}
        </h3>
      )}
      <div className="space-y-1">{children}</div>
    </div>
  );
};

export const EnhancedSidebar = ({
  children,
  className,
  collapsed = false,
}: SidebarProps) => {
  return (
    <aside
      className={cn(
        'flex flex-col h-full border-r bg-background p-3 transition-all duration-300',
        collapsed ? 'w-16' : 'w-64',
        className,
      )}
    >
      <div className="flex-1 overflow-y-auto py-2 scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
        {children}
      </div>
    </aside>
  );
};

// Example usage component
export const SidebarExample = () => {
  const [activeItem, setActiveItem] = React.useState('profile');

  const handleItemClick = (itemId: string) => (e: React.MouseEvent) => {
    e.preventDefault();
    setActiveItem(itemId);
  };

  return (
    <EnhancedSidebar>
      <div className="mb-6 px-3">
        <h2 className="text-lg font-bold">Beauty CRM</h2>
      </div>

      <SidebarSection title="Main">
        <SidebarItem
          icon={Home}
          label="Profile"
          href="/profile"
          isActive={activeItem === 'profile'}
          onClick={handleItemClick('profile')}
        />

        <SidebarItem
          icon={Calendar}
          label="Calendar"
          href="/calendar"
          isActive={activeItem === 'calendar'}
          onClick={handleItemClick('calendar')}
        />
      </SidebarSection>

      <SidebarSection title="Settings" className="mt-6">
        <SidebarItem
          icon={Settings}
          label="Settings"
          href="/settings"
          isActive={activeItem === 'settings'}
          onClick={handleItemClick('settings')}
        />

        <SidebarItem
          icon={Users}
          label="Account"
          href="/account"
          isActive={activeItem === 'account'}
          onClick={handleItemClick('account')}
        />
      </SidebarSection>
    </EnhancedSidebar>
  );
};
