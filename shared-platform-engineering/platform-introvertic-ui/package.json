{"dependencies": {"@glideapps/glide-data-grid": "^6.0.3", "@hookform/resolvers": "^5.2.2", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.8", "@radix-ui/react-avatar": "^1.1.11", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.8", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.8", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.8", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.4", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.21.3", "@testing-library/react": "^16.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.24.10", "input-otp": "^1.4.2", "lucide-react": "^0.562.0", "next-themes": "^0.4.6", "react": "^19.2.3", "react-day-picker": "^9.13.0", "react-hook-form": "^7.70.0", "react-resizable-panels": "^4.3.0", "recharts": "^3.6.0", "shadcn-ui": "^0.9.5", "sonner": "^2.0.7", "tailwind-merge": "^3.4.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^4.3.5"}, "description": "ShadC<PERSON> UI, Radix UI manager, to add components use $ bunx shadcn@latest add <component>", "devDependencies": {"@chromatic-com/storybook": "^4.1.3", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-onboarding": "^10.1.11", "@storybook/addon-styling": "^1.3.7", "@storybook/addon-themes": "^10.1.11", "@storybook/blocks": "^8.6.14", "@storybook/builder-vite": "^10.1.11", "@storybook/react": "^10.1.11", "@storybook/react-vite": "^10.1.11", "@storybook/test": "^8.6.15", "@swc-node/register": "^1.11.1", "@swc/core": "^1.15.8", "@tailwindcss/vite": "^4.1.18", "@testing-library/jest-dom": "^6.9.1", "@types/node": "^25.0.3", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@vitejs/plugin-react": "^5.1.2", "jsdom": "^27.4.0", "react-dom": "^19.2.3", "storybook": "^10.0.4", "tailwindcss": "^4.1.16", "tsup": "^8.5.1", "typescript": "^5.9.3", "vite": "^7.3.1"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./dist/index.css": "./dist/index.css", "./styles": "./dist/index.css", "./styles.css": "./dist/index.css"}, "files": ["dist/**"], "main": "./dist/index.js", "module": "./dist/index.js", "name": "@beauty-crm/platform-introvertic-ui", "publishConfig": {"access": "public"}, "scripts": {"build": "bunx --bun tsup && bunx vite build --config vite.css.config.ts", "build-storybook": "NODE_ENV=production storybook build", "clean": "rm -rf dist", "dev": "bunx --bun tsup --watch", "drop-down-menu": "bunx shadcn@latest add dropdown-menu", "format": "biome format --write --fix .", "lint": "biome lint . --config-path ../../biome.json", "lint:check": "biome check . --config-path ../../biome.json", "lint:fix": "biome lint --write . --config-path ../../biome.json", "prepack": "bun run build", "storybook": "NODE_ENV=development storybook dev -p 6006", "tabs": "bunx shadcn@latest add tabs", "tooltip": "bunx shadcn@latest add tooltip", "publish": "npm publish --registry http://verdaccio.localhost:4873 --force"}, "sideEffects": ["**/*.css"], "style": "./dist/index.css", "type": "module", "types": "./dist/index.d.ts", "version": "1.0.2"}