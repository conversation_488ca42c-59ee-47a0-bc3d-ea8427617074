{"dependencies": {"@beauty-crm/platform-environment-names": "^1.0.0", "@beauty-crm/platform-logger": "^1.0.3", "@beauty-crm/product-responses": "^1.0.0", "@hono/node-server": "^1.19.7", "hono": "^4.11.3", "ioredis": "^5.9.0"}, "description": "Platform runtime environment management, configuration, and execution context handling", "devDependencies": {"@types/node": "^25.0.3", "rimraf": "^6.1.2", "typescript": "^5.9.3"}, "main": "dist/index.js", "name": "@beauty-crm/platform-computing-runtime", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for platform-computing-runtime' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}