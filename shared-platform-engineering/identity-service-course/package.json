{"dependencies": {"framer-motion": "^12.24.10", "prismjs": "^1.30.0", "react": "^19.2.3", "react-dom": "^19.2.3", "react-icons": "^5.5.0", "react-router-dom": "^7.12.0", "react-syntax-highlighter": "^16.1.0", "zustand": "^5.0.9"}, "description": "Interactive course platform for understanding the Identity Service", "devDependencies": {"@biomejs/biome": "^2.3.11", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.18", "typescript": "^5.9.3", "vite": "^7.3.1", "vitest": "^4.0.6"}, "main": "src/index.ts", "name": "@beauty-crm/identity-service-course", "scripts": {"build": "tsc && nx vite:build", "dev": "nx serve", "format": "bunx @biomejs/biome format . --write", "lint": "bunx @biomejs/biome lint .", "preview": "nx vite:preview"}, "version": "1.0.0"}