{"dependencies": {"@beauty-crm/platform-computing-lifecycle": "^1.0.0", "@beauty-crm/platform-environment-names": "^1.0.0", "@beauty-crm/product-domain-types": "^1.0.0", "@prisma/client": "^7.2.0", "vitest": "^3.1.4"}, "devDependencies": {"@types/node": "^25.0.3", "rimraf": "^6.1.2", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/platform-test-runner", "publishConfig": {"access": "public"}, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for platform-test-runner' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}