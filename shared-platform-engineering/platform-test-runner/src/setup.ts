import { afterAll, afterEach, beforeAll, beforeEach, vi } from 'vitest';
import { MockKVNamespace, MockPrismaClient } from './mocks.js';

interface ExtendedGlobal {
  KVNamespace?: typeof MockKVNamespace; // biome-ignore lint/style/useNamingConvention: Matches actual KVNamespace class name for test mocking
  PrismaClient?: typeof MockPrismaClient; // biome-ignore lint/style/useNamingConvention: Matches actual PrismaClient class name for test mocking
}

declare global {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface Window extends ExtendedGlobal {}
}

declare module 'vitest' {
  interface TestContext {
    KVNamespace: typeof MockKVNamespace; // biome-ignore lint/style/useNamingConvention: Matches actual KVNamespace class name for test mocking
    PrismaClient: typeof MockPrismaClient; // biome-ignore lint/style/useNamingConvention: Matches actual PrismaClient class name for test mocking
  }
}

const mockGlobals = {
  KVNamespace: MockKVNamespace,
  PrismaClient: MockPrismaClient,
} as const;

beforeAll(() => {
  // Setup mocks
  for (const _key of Object.keys(mockGlobals)) {
  }
});

afterAll(() => {
  // Cleanup mocks
  for (const _key of Object.keys(mockGlobals)) {
  }
});

export function setupTestEnvironment() {
  let db!: MockPrismaClient;
  let kv!: MockKVNamespace;

  beforeAll(() => {
    db = new MockPrismaClient();
    kv = new MockKVNamespace();
    // Setup mocks
    (globalThis as ExtendedGlobal).KVNamespace = MockKVNamespace;
    (globalThis as ExtendedGlobal).PrismaClient = MockPrismaClient;
  });

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up after each test
  });

  afterAll(() => {
    // Clean up after all tests
    // Cleanup mocks
    (globalThis as ExtendedGlobal).KVNamespace = undefined;
    (globalThis as ExtendedGlobal).PrismaClient = undefined;
  });

  return {
    db,
    kv,
  };
}

export function createTestConfig() {
  return {
    globalSetup: setupTestEnvironment,
    testTimeout: 10000,
  };
}
