{"author": "Beauty CRM Platform Team", "dependencies": {"@beauty-crm/platform-environment-names": "^1.0.0", "@beauty-crm/platform-logger": "^1.0.3", "@beauty-crm/product-db-names": "^1.0.0", "@beauty-crm/product-domain-types": "^1.0.0", "@prisma/adapter-pg": "^7.2.0", "@prisma/client": "^7.2.0", "pg": "^8.13.1", "uuid": "^13.0.0"}, "description": "Database infrastructure and connection management utilities for platform services", "devDependencies": {"@types/pg": "^8.16.0", "@types/uuid": "^11.0.0", "prisma": "^7.2.0", "rimraf": "^6.1.2", "typescript": "^5.8.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-db-client", "publishConfig": {"access": "public"}, "scripts": {"build": "bunx prisma generate && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "db:generate": "bunx prisma generate", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "outdated": "bun outdated", "postinstall": "node validate-dependencies.cjs", "test": "echo 'No tests specified for platform-db-client' && exit 0", "unpublish": "npm unpublish @beauty-crm/platform-db-client@1.0.2 --registry http://verdaccio.localhost:4873 --force"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.4"}