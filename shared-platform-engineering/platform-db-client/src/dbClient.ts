import { Logger } from '@beauty-crm/platform-logger';
import { v4 as uuidv4 } from 'uuid';

// Import Prisma with proper typing and Prisma 7 adapter
import { PrismaClient } from '@prisma/client';
import { PrismaPg } from '@prisma/adapter-pg';
import pg from 'pg';

export { PrismaClient };

interface DbLogMetadata {
  requestId: string;
  timestamp: string;
  operation?: string;
  duration?: number;
  error?: {
    message: string;
    code?: string;
    stack?: string;
  };
}

function isPrismaError(error: unknown): error is { code: string } {
  return error !== null && typeof error === 'object' && 'code' in error;
}

class DbClient extends PrismaClient {
  private static instance: DbClient;
  private initialized = false;

  private constructor() {
    // Initialize Prisma 7 adapter with PostgreSQL
    const connectionString =
      process.env.DATABASE_URL ||
      '********************************************************************/beauty_crm';
    const pool = new pg.Pool({ connectionString });
    const adapter = new PrismaPg(pool);

    super({
      adapter,
      errorFormat: 'pretty',
      log: [
        { emit: 'event', level: 'query' },
        { emit: 'event', level: 'error' },
        { emit: 'event', level: 'info' },
        { emit: 'event', level: 'warn' },
      ],
    });

    this.$extends({
      query: {
        async $allOperations({
          operation,
          args,
          query,
        }: {
          operation: string;
          args: unknown;
          query: (args: unknown) => Promise<unknown>;
        }) {
          const requestId = uuidv4();
          const startTime = Date.now();
          const metadata: DbLogMetadata = {
            operation,
            requestId,
            timestamp: new Date().toISOString(),
          };

          try {
            Logger.info('Operation started', metadata);
            const result = await query(args);
            metadata.duration = Date.now() - startTime;
            Logger.info('Operation completed', { ...metadata, result });
            return result;
          } catch (error) {
            metadata.duration = Date.now() - startTime;
            metadata.error = {
              code: isPrismaError(error) ? error.code : undefined,
              message: error instanceof Error ? error.message : 'Unknown error',
              stack: error instanceof Error ? error.stack : undefined,
            };
            Logger.error('Operation failed', metadata);
            throw error;
          }
        },
      },
    });
  }

  public static getInstance(): DbClient {
    if (!DbClient.instance) {
      DbClient.instance = new DbClient();
    }
    return DbClient.instance;
  }

  public async connect(): Promise<void> {
    if (this.initialized) return;

    const metadata: DbLogMetadata = {
      operation: 'connect',
      requestId: uuidv4(),
      timestamp: new Date().toISOString(),
    };

    try {
      Logger.info('Connecting to database', metadata);
      await this.$connect();
      this.initialized = true;
      Logger.info('Connected to database', metadata);
    } catch (error) {
      metadata.error = {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      };
      Logger.error('Connection failed', metadata);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    const metadata: DbLogMetadata = {
      operation: 'disconnect',
      requestId: uuidv4(),
      timestamp: new Date().toISOString(),
    };

    try {
      Logger.info('Disconnecting from database', metadata);
      await this.$disconnect();
      this.initialized = false;
      Logger.info('Disconnected from database', metadata);
    } catch (error) {
      metadata.error = {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      };
      Logger.error('Disconnection failed', metadata);
      throw error;
    }
  }
}

const prisma = DbClient.getInstance();
export const connectDb = () => prisma.connect();
export default prisma;
export type PrismaClientType = typeof prisma;
export type { DbClient };
