{"bin": {"mdblaster": "./bin/mdblaster"}, "dependencies": {"chalk": "^5.4.1"}, "devDependencies": {"@types/node": "^25.0.3", "typescript": "^5.8.3", "vitest": "^4.0.6"}, "files": ["dist", "lib", "pkg", "bin", "mdblaster.gemspec"], "main": "./dist/index.js", "name": "@beauty-crm/platform-mdblaster", "scripts": {"build": "npm run clean && npm run build:ts", "build:gem": "gem build mdblaster.gemspec", "build:ts": "tsc", "clean": "rm -rf dist pkg/*.gem coverage", "dev": "tsc --watch", "mdblaster": "./bin/mdblaster", "mdblaster:test": "./bin/mdblaster tests/api/auth.blast.md", "postbuild": "chmod +x ./bin/mdblaster && npm link", "test": "vitest", "test:watch": "vitest watch"}, "type": "module", "types": "./dist/index.d.ts", "version": "1.0.0"}