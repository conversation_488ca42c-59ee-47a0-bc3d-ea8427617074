{"bin": {"generate-roadmap": "./bin/generate-roadmap.js"}, "dependencies": {"@beauty-crm/platform-utilities": "^1.0.0"}, "devDependencies": {"@types/node": "^25.0.3", "rimraf": "^6.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/platform-roadmap-generator", "private": true, "scripts": {"build": "tsc --build && chmod +x ./bin/generate-roadmap.sh", "clean": "<PERSON><PERSON><PERSON> dist", "generate": "./bin/generate-roadmap.sh"}, "types": "dist/index.d.ts", "version": "1.0.0"}