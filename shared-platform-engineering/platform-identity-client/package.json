{"dependencies": {"@beauty-crm/product-identity-types": "^1.0.0", "bcryptjs": "^3.0.3", "jsonwebtoken": "^9.0.3", "otplib": "^12.0.1", "zod": "^4.3.5"}, "description": "Shared identity client for Beauty CRM", "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^25.0.3", "rimraf": "^6.1.2", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/platform-identity-client", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "format": "biome format --write --fix .", "lint": "biome lint . --config-path ../../biome.json", "lint:check": "biome check . --config-path ../../biome.json", "lint:fix": "biome lint --write . --config-path ../../biome.json"}, "types": "dist/index.d.ts", "version": "1.0.0"}