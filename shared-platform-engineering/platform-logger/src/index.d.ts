interface LoggerInterface {
  level: string;
  debug: (obj: Record<string, unknown>, msg?: string) => void;
  info: (obj: Record<string, unknown>, msg?: string) => void;
  warn: (obj: Record<string, unknown>, msg?: string) => void;
  error: (obj: Record<string, unknown>, msg?: string) => void;
}
export declare const logger: LoggerInterface;
export default logger;
export declare enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}
export declare function log(
  level: LogLevel,
  message: string,
  ...optionalParams: unknown[]
): void;
export declare function debug(
  message: string,
  ...optionalParams: unknown[]
): void;
export declare function info(
  message: string,
  ...optionalParams: unknown[]
): void;
export declare function warn(
  message: string,
  ...optionalParams: unknown[]
): void;
export declare function error(
  message: string,
  ...optionalParams: unknown[]
): void;
export declare const Logger: {
  debug: typeof debug;
  error: typeof error;
  info: typeof info;
  log: typeof log;
  warn: typeof warn;
};
//# sourceMappingURL=index.d.ts.map
