// Simple console-based logger to avoid pino bundling issues in Docker
// pino's thread-stream and pino-pretty cause ModuleNotFound errors when bundled
const createLogger = () => {
  const logLevel = process.env.LOG_LEVEL || 'info';
  const levels = { debug: 20, info: 30, warn: 40, error: 50 };
  const currentLevel = levels[logLevel] || 30;
  return {
    level: logLevel,
    debug: (obj, msg) => {
      if (currentLevel <= 20)
        console.log(JSON.stringify({ level: 'debug', ...obj, msg }));
    },
    info: (obj, msg) => {
      if (currentLevel <= 30)
        console.log(JSON.stringify({ level: 'info', ...obj, msg }));
    },
    warn: (obj, msg) => {
      if (currentLevel <= 40)
        console.log(JSON.stringify({ level: 'warn', ...obj, msg }));
    },
    error: (obj, msg) => {
      if (currentLevel <= 50)
        console.error(JSON.stringify({ level: 'error', ...obj, msg }));
    },
  };
};
export const logger = createLogger();
export default logger;
// Export LogLevel enum for compatibility with tests
// Initialize LogLevel as an empty object
export var LogLevel = {};

// Initialize LogLevel enum values
(() => {
  LogLevel.DEBUG = 'DEBUG';
  LogLevel.INFO = 'INFO';
  LogLevel.WARN = 'WARN';
  LogLevel.ERROR = 'ERROR';
})();
// Export Logger functions for compatibility with tests
export function log(level, message, ...optionalParams) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`, ...optionalParams);
}
export function debug(message, ...optionalParams) {
  // In some environments, process may not exist.
  // If available and not in prod, log debug messages.
  if (typeof process !== 'undefined' && process.env?.NODE_ENV !== 'prod') {
    log(LogLevel.DEBUG, message, ...optionalParams);
  }
}
export function info(message, ...optionalParams) {
  log(LogLevel.INFO, message, ...optionalParams);
}
export function warn(message, ...optionalParams) {
  log(LogLevel.WARN, message, ...optionalParams);
}
export function error(message, ...optionalParams) {
  log(LogLevel.ERROR, message, ...optionalParams);
}
// Export Logger class for backward compatibility
// Export Logger functions for backward compatibility
export const Logger = {
  debug,
  error,
  info,
  log,
  warn,
};
//# sourceMappingURL=index.js.map
