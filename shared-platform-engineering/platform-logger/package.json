{"dependencies": {}, "description": "Logging utilities for Beauty CRM", "devDependencies": {"@types/node": "^25.0.3", "tsup": "^8.5.1", "typescript": "^5.8.3", "vitest": "^4.0.6"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "files": ["dist/**"], "main": "./dist/index.js", "module": "./dist/index.js", "name": "@beauty-crm/platform-logger", "publishConfig": {"access": "public"}, "scripts": {"build": "bunx --bun tsup", "build:tsc": "tsc --build", "clean": "rm -rf dist .tsbuildinfo", "dev": "bunx --bun tsup --watch", "dev:tsc": "tsc --build --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prepack": "bun run build"}, "type": "module", "types": "./dist/index.d.ts", "version": "1.0.3"}