{"author": "Beauty CRM Platform Team", "description": "Environment name constants and utilities for Beauty CRM", "devDependencies": {"@types/node": "^25.0.3", "tsup": "^8.5.1", "typescript": "^5.8.3"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist/**"], "keywords": [], "license": "ISC", "main": "./dist/index.js", "module": "./dist/index.js", "name": "@beauty-crm/platform-environment-names", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc --build", "build:bundle": "bunx --bun tsup", "clean": "rm -rf dist .tsbuildinfo", "dev": "tsc --build --watch", "dev:bundle": "bunx --bun tsup --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prepack": "bun run build"}, "type": "module", "types": "./dist/index.d.ts", "version": "1.0.0"}