import chalk from 'chalk';
import type { Command } from 'commander';
import inquirer from 'inquirer';
import {
  createDefaultMetadataConfig,
  readMetadataConfig,
} from '../../core/metadata-reader.js';

export function metadataCommand(program: Command): void {
  program
    .command('init')
    .description(
      'Initialize a new metadata file for your module federation project',
    )
    .option('--app-type <type>', 'Type of app (shell, remote)')
    .option('--app-name <n>', 'Name of the app')
    .option('--app-env <env>', 'Environment (dev, prod)', 'dev')
    .option('--port <port>', 'Port number', '5174')
    .option(
      '--framework <framework>',
      'Federation framework to use (nx, vite)',
      'vite',
    )
    .action(async (options) => {
      try {
        // Check if metadata file already exists
        const existingMetadata = readMetadataConfig();
        if (existingMetadata) {
          console.log(chalk.yellow('Metadata file already exists.'));

          const { overwrite } = await inquirer.prompt([
            {
              default: false,
              message: 'Do you want to overwrite the existing metadata file?',
              name: 'overwrite',
              type: 'confirm',
            },
          ]);

          if (!overwrite) {
            console.log(
              chalk.yellow(
                'Operation cancelled. Existing metadata file was not modified.',
              ),
            );
            return;
          }
        }

        // Prompt for required fields if not provided
        const prompts = [];

        if (!options.appType) {
          prompts.push({
            choices: [
              { name: 'Shell (Host) Application', value: 'shell' },
              { name: 'Remote (Federated) Application', value: 'remote' },
            ],
            default: 'remote',
            message: 'Select application type:',
            name: 'appType',
            type: 'list',
          });
        }

        if (!options.appName) {
          prompts.push({
            default: 'my-app',
            message: 'Enter application name:',
            name: 'appName',
            type: 'input',
          });
        }

        if (!options.framework) {
          prompts.push({
            choices: [
              { name: 'Vite Module Federation', value: 'vite' },
              { name: 'Nx Module Federation', value: 'nx' },
            ],
            default: 'vite',
            message: 'Choose a module federation framework:',
            name: 'framework',
            type: 'list',
          });
        }

        let answers = {} as Record<string, string>;
        if (prompts.length > 0) {
          // Handle inquirer.prompt type incompatibility
          answers = await inquirer.prompt(prompts);
        }

        const appType = (options.appType || answers.appType) as
          | 'shell'
          | 'remote';
        const appName = options.appName || answers.appName;
        const framework = (options.framework || answers.framework) as
          | 'nx'
          | 'vite';

        // Create default metadata file
        const metadata = createDefaultMetadataConfig(appName, appType);

        // Override with provided options
        metadata.framework = framework;
        if (options.appEnv) metadata.appEnv = options.appEnv as 'dev' | 'prod';
        if (options.port) metadata.port = options.port;

        console.log(chalk.green('Metadata file created successfully!'));
        console.log(
          chalk.blue(
            "You can now run: 'platform-shell-lifecycle start' to start your application",
          ),
        );
      } catch (error) {
        console.error(chalk.red('Error:'), error);
        process.exit(1);
      }
    });

  program
    .command('show')
    .description('Show the current metadata configuration')
    .action(() => {
      try {
        const metadata = readMetadataConfig();
        if (!metadata) {
          console.log(
            chalk.yellow(
              "No metadata file found. Run 'platform-shell-lifecycle init' to create one.",
            ),
          );
          return;
        }

        console.log(chalk.blue('Current metadata configuration:'));
        console.log(JSON.stringify(metadata, null, 2));
      } catch (error) {
        console.error(chalk.red('Error:'), error);
        process.exit(1);
      }
    });
}
