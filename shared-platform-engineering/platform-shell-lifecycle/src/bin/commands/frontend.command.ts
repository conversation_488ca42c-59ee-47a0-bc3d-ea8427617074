import chalk from 'chalk';
import type { Command } from 'commander';
import inquirer from 'inquirer';
import type { AppOptions } from '../../core/federation-dev.js';
import { startFederatedApp } from '../../core/federation-dev.js';
import {
  createDefaultMetadataConfig,
  readMetadataConfig,
} from '../../core/metadata-reader.js';

export function frontendCommand(program: Command): void {
  program
    .command('start')
    .description('Start a federated micro-frontend in development mode')
    .option('--app-type <type>', 'Type of app (frontend, shell)')
    .option('--app-name <n>', 'Name of the app')
    .option('--app-env <env>', 'Environment (dev, prod)', 'dev')
    .option('--port <port>', 'Port number')
    .option(
      '--framework <framework>',
      'Federation framework to use (nx, vite)',
      'vite',
    )
    .option('--config <config>', 'Path to custom webpack/vite config file')
    .option('--init', 'Initialize a new metadata file')
    .action(async (options) => {
      try {
        // Read config from metadata file or prompt for required fields if not found
        let metadata = readMetadataConfig();

        // Initialize metadata file if requested
        if (options.init || !metadata) {
          // Prompt for required fields if not provided
          const prompts = [];

          if (!options.appType) {
            prompts.push({
              choices: [
                { name: 'Shell (Host) Application', value: 'shell' },
                { name: 'Remote (Federated) Application', value: 'remote' },
              ],
              default: 'remote',
              message: 'Select application type:',
              name: 'appType',
              type: 'list',
            });
          }

          if (!options.appName) {
            prompts.push({
              default: 'my-app',
              message: 'Enter application name:',
              name: 'appName',
              type: 'input',
            });
          }

          let answers: Record<string, string> = {};
          if (prompts.length > 0) {
            // Handle inquirer.prompt type incompatibility
            answers = await inquirer.prompt(prompts);
          }

          const appType = (options.appType || answers.appType) as
            | 'shell'
            | 'remote';
          const appName = options.appName || answers.appName;

          // Create default metadata file
          metadata = createDefaultMetadataConfig(appName, appType);

          console.log(chalk.green('Metadata file created successfully!'));
        }

        // Override metadata with command line options if provided
        if (options.appType)
          metadata.appType = options.appType as 'shell' | 'remote';
        if (options.appName) metadata.appName = options.appName;
        if (options.appEnv) metadata.appEnv = options.appEnv as 'dev' | 'prod';
        // Only override port from command line if explicitly provided
        if (options.port) metadata.port = options.port;
        // Use default port of 5174 only if no port is specified in metadata
        if (!metadata.port) metadata.port = '5174';
        if (options.framework)
          metadata.framework = options.framework as 'nx' | 'vite';
        if (options.config) metadata.configPath = options.config;

        // Ask for framework if not specified in metadata or command line
        if (!metadata.framework) {
          const answers = await inquirer.prompt([
            {
              choices: [
                { name: 'Vite Module Federation', value: 'vite' },
                { name: 'Nx Module Federation', value: 'nx' },
              ],
              default: 'vite',
              message: 'Choose a module federation framework:',
              name: 'framework',
              type: 'list',
            },
          ]);
          metadata.framework = answers.framework as 'nx' | 'vite';
        }

        console.log(
          chalk.blue(
            `🚀 Starting ${metadata.appName} in ${metadata.appEnv} mode using ${metadata.framework === 'nx' ? 'Nx' : 'Vite'} Module Federation`,
          ),
        );

        const appOptions: AppOptions = {
          appEnv: metadata.appEnv || 'dev',
          appName: metadata.appName,
          appType: metadata.appType,
          configPath: metadata.configPath,
          framework: metadata.framework,
          port: metadata.port || '5174',
        };

        await startFederatedApp({
          ...appOptions,
          metadata, // Pass metadata as an extra parameter
        });
      } catch (error) {
        console.error(chalk.red('Error:'), error);
        process.exit(1);
      }
    });
}
