import path from 'node:path';
import chalk from 'chalk';
import fs from 'fs-extra';
import ora from 'ora';
import type { MetadataConfig } from './metadata-reader.js';

interface NxConfigOptions {
  projectName: string;
  projectType: 'shell' | 'remote';
  port: string;
  metadata?: MetadataConfig;
}

// Define more specific types for build configuration
interface BuildOptions {
  outputPath: string;
}

interface BuildConfiguration {
  mode: string;
}

interface BuildTarget {
  executor: string;
  outputs: string[];
  defaultConfiguration: string;
  options: BuildOptions;
  configurations: {
    development: BuildConfiguration;
    production: BuildConfiguration;
  };
}

interface ServeOptions {
  buildTarget: string;
  port: number;
  liveReload?: boolean;
}

interface ServeTarget {
  executor: string;
  defaultConfiguration: string;
  options: ServeOptions;
  configurations: {
    development: { buildTarget: string };
    production: { buildTarget: string };
  };
}

interface ProjectJson {
  name: string;
  $schema: string;
  projectType: string;
  sourceRoot: string;
  targets: {
    build: BuildTarget;
    serve: ServeTarget;
    [key: string]: ServeTarget | BuildTarget;
  };
}

/**
 * Generate Nx configuration files for Module Federation
 * @param options Configuration options
 */
export async function generateNxConfig(
  options: NxConfigOptions,
): Promise<void> {
  const { projectName, projectType, port, metadata } = options;
  const cwd = process.cwd();
  const spinner = ora(
    'Generating Nx Module Federation configuration...',
  ).start();

  try {
    // Check if nx.json exists, if not create it
    const nxJsonPath = path.join(cwd, 'nx.json');
    if (!fs.existsSync(nxJsonPath)) {
      const nxJson = {
        affected: {
          defaultBase: 'main',
        },
        extends: 'nx/presets/npm.json',
        tasksRunnerOptions: {
          default: {
            options: {
              cacheableOperations: ['build', 'lint', 'test', 'e2e'],
            },
            runner: 'nx/tasks-runners/default',
          },
        },
      };

      fs.writeFileSync(nxJsonPath, JSON.stringify(nxJson, null, 2));
    }

    // Create project.json for the current project
    const projectJsonPath = path.join(cwd, 'project.json');
    const projectJson: ProjectJson = {
      $schema: 'node_modules/nx/schemas/project-schema.json', // biome-ignore lint/style/useNamingConvention: Standard JSON Schema property name
      name: projectName,
      projectType: 'application',
      sourceRoot: 'src',
      targets: {
        build: {
          configurations: {
            development: {
              mode: 'development',
            },
            production: {
              mode: 'production',
            },
          },
          defaultConfiguration: 'production',
          executor: '@nx/webpack:webpack',
          options: {
            outputPath: 'dist',
          },
          outputs: ['{options.outputPath}'],
        },
        serve: {
          configurations: {
            development: {
              buildTarget: `${projectName}:build:development`,
            },
            production: {
              buildTarget: `${projectName}:build:production`,
            },
          },
          defaultConfiguration: 'development',
          executor: '@nx/webpack:dev-server',
          options: {
            buildTarget: `${projectName}:build`,
            port: Number.parseInt(port, 10),
          },
        },
      },
    };

    // Add module federation specific targets
    if (projectType === 'shell') {
      projectJson.targets['serve-host'] = {
        configurations: {
          development: {
            buildTarget: `${projectName}:build:development`,
          },
          production: {
            buildTarget: `${projectName}:build:production`,
          },
        },
        defaultConfiguration: 'development',
        executor: '@nx/module-federation:dev-server',
        options: {
          buildTarget: `${projectName}:build`,
          liveReload: true,
          port: Number.parseInt(port, 10),
        },
      };
    } else {
      projectJson.targets['serve-remote'] = {
        configurations: {
          development: {
            buildTarget: `${projectName}:build:development`,
          },
          production: {
            buildTarget: `${projectName}:build:production`,
          },
        },
        defaultConfiguration: 'development',
        executor: '@nx/module-federation:dev-server',
        options: {
          buildTarget: `${projectName}:build`,
          liveReload: true,
          port: Number.parseInt(port, 10),
        },
      };
    }

    fs.writeFileSync(projectJsonPath, JSON.stringify(projectJson, null, 2));

    // Use metadata for module federation config if available
    const nxConfig = metadata?.nx || {
      name: projectName,
      ...(projectType === 'shell'
        ? {
            remotes: [],
          }
        : {
            exposes: {},
          }),
      shared: {
        react: { eager: projectType === 'shell', singleton: true },
        'react-dom': { eager: projectType === 'shell', singleton: true },
      },
    };

    // Create or update module-federation.config.js
    const mfConfigPath = path.join(cwd, 'module-federation.config.js');
    let mfConfig: string;

    if (projectType === 'shell') {
      mfConfig = `
module.exports = {
  name: '${nxConfig.name}',
  remotes: [
    ${
      nxConfig.remotes?.length
        ? nxConfig.remotes
            .map(([name, url]) => `['${name}', '${url}']`)
            .join(',\n    ')
        : "// Add your remote entries here\n    // Example: ['remote1', 'http://localhost:4201/remoteEntry.js']"
    }
  ],
  shared: ${JSON.stringify(nxConfig.shared, null, 2).replace(/"([^"]+)":/g, '$1:')}
};`;
    } else {
      mfConfig = `
module.exports = {
  name: '${nxConfig.name}',
  exposes: ${JSON.stringify(nxConfig.exposes || {}, null, 2)
    .replace(/"([^"]+)":/g, '$1:')
    .replace(
      /^{[\s\n]*}$/m,
      "{\n    // Define your exposed modules here\n    // Example: './Component': './src/components/Component.tsx'\n  }",
    )},
  shared: ${JSON.stringify(nxConfig.shared, null, 2).replace(/"([^"]+)":/g, '$1:')}
};`;
    }

    fs.writeFileSync(mfConfigPath, mfConfig);

    // Create webpack.config.ts based on example
    const webpackConfigPath = path.join(cwd, 'webpack.config.ts');
    const webpackConfig = `
import { composePlugins, withNx } from '@nx/webpack';
import { withReact } from '@nx/react';
import { withModuleFederation } from '@nx/react/module-federation';
import baseConfig from './module-federation.config';

const config = {
  ...baseConfig,
};

// Nx plugins for webpack to build config object from Nx options and context.
export default composePlugins(
  withNx(),
  withReact(),
  withModuleFederation(config, { dts: false })
);
`;

    fs.writeFileSync(webpackConfigPath, webpackConfig);

    // Create a vite+webpack hybrid configuration option
    const viteWebpackConfigPath = path.join(cwd, 'vite.config.ts');

    // Use metadata for vite config if available
    const viteConfig = metadata?.vite || {
      name: projectName,
      ...(projectType === 'shell'
        ? {
            remotes: {},
          }
        : {
            exposes: {},
          }),
      shared: {
        react: { eager: projectType === 'shell', singleton: true },
        'react-dom': { eager: projectType === 'shell', singleton: true },
      },
    };

    if (!fs.existsSync(viteWebpackConfigPath)) {
      // Following the example from module-federation.io
      const viteWebpackConfig = `
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import federation from '@module-federation/vite';

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: '${viteConfig.name}',
      filename: 'remoteEntry.js',
      ${
        projectType === 'shell'
          ? `remotes: ${JSON.stringify(viteConfig.remotes || {}, null, 2)
              .replace(/"([^"]+)":/g, '$1:')
              .replace(
                /^{[\s\n]*}$/m,
                "{\n        // Add your remotes here\n        // remote1: 'http://localhost:3001/remoteEntry.js',\n      }",
              )},`
          : `exposes: ${JSON.stringify(viteConfig.exposes || {}, null, 2)
              .replace(/"([^"]+)":/g, '$1:')
              .replace(
                /^{[\s\n]*}$/m,
                "{\n        // Define your exposed modules here\n        // './Component': './src/components/Component.tsx',\n      }",
              )},`
      }
      shared: ${JSON.stringify(viteConfig.shared || {}, null, 2).replace(/"([^"]+)":/g, '$1:')}
    })
  ],
  // Following the module-federation.io example
  build: {
    modulePreload: false,
    target: 'esnext',
    minify: false,
    cssCodeSplit: false
  },
  // Add development server configuration
  server: {
    port: ${Number.parseInt(port, 10)},
    open: true
  }
});
`;
      fs.writeFileSync(viteWebpackConfigPath, viteWebpackConfig);
    }

    spinner.succeed('Nx Module Federation configuration generated');
    console.log(
      chalk.green(`
Configuration files created:
- nx.json
- project.json
- module-federation.config.js
- webpack.config.ts
- vite.config.ts (if not existing)

This configuration allows switching between Webpack and Vite for Module Federation.
You may need to customize the configuration files to properly configure your remotes and exposed modules.
    `),
    );
  } catch (error) {
    spinner.fail('Failed to generate Nx configuration');
    console.error(chalk.red('An error occurred:'), error);
    throw error;
  }
}
