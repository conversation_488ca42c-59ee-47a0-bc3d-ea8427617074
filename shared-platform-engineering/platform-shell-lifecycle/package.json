{"bin": {"platform-shell-lifecycle": "./dist/bin/cli.js"}, "dependencies": {"@module-federation/vite": "^1.9.4", "@vitejs/plugin-react": "^4.5.0", "chalk": "^5.4.1", "commander": "^14.0.0", "cross-spawn": "^7.0.6", "dotenv": "^17.2.3", "fs-extra": "^11.3.3", "inquirer": "^13.1.0", "ora": "^9.0.0", "vite": "^6.3.5", "webpack": "^5.104.1", "webpack-dev-server": "^5.2.1"}, "description": "Smart development lifecycle tool for Module Federation microfrontends", "devDependencies": {"@types/cross-spawn": "^6.0.6", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.8", "@types/node": "^25.0.3", "typescript": "^5.8.3"}, "files": ["dist", "src"], "main": "dist/index.js", "name": "@beauty-crm/platform-shell-lifecycle", "scripts": {"build": "tsc", "dev": "tsc -w", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "postbuild": "chmod +x dist/bin/cli.js && npm link"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}