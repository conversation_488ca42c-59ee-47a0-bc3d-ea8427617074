{"author": "Beauty CRM Platform Team", "bugs": {"url": "https://github.com/odykyi/beauty-crm/issues"}, "dependencies": {"@prisma/client": "^7.2.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "nats": "^2.28.2", "zod": "^4.3.5"}, "description": "Beautiful event-driven architecture with NATS JetStream - the eventing platform for Beauty CRM", "devDependencies": {"@biomejs/biome": "^2.3.11", "@types/node": "^25.0.3", "@vitest/coverage-v8": "^4.0.16", "typescript": "^5.8.3", "vitest": "^4.0.6"}, "files": ["dist", "README.md"], "homepage": "https://github.com/odykyi/beauty-crm/tree/main/shared-platform-engineering/platform-eventing#readme", "keywords": ["nats", "jetstream", "events", "messaging", "microservices", "beauty-crm", "eventing"], "license": "MIT", "main": "dist/index.js", "name": "@beauty-crm/platform-eventing", "publishConfig": {"access": "public"}, "repository": {"directory": "shared-platform-engineering/platform-eventing", "type": "git", "url": "git+https://github.com/odykyi/beauty-crm.git"}, "scripts": {"build": "tsc", "check": "biome check .", "dev": "tsc --watch", "format": "biome format --write .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "test": "vitest", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "unpublish": "npm unpublish @beauty-crm/platform-eventing@1.0.0 --registry http://verdaccio.localhost:4873", "publish": "npm publish --registry http://verdaccio.localhost:4873 --force"}, "types": "dist/index.d.ts", "version": "1.0.4"}