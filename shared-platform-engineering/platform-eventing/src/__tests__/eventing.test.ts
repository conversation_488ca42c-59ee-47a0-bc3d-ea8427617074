import { connect } from 'nats';
import { v4 as uuidv4 } from 'uuid';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  createEvent,
  EventPublisher,
  EventSubscriber,
  EventTypes,
} from '../index';

// Create a shared jetstream mock that persists across calls
const mockJetStreamClient = {
  consumers: vi.fn(() => ({
    get: vi.fn(),
  })),
  publish: vi.fn(),
  streams: vi.fn(() => ({
    get: vi.fn(),
  })),
  subscribe: vi.fn(() => ({
    ack: vi.fn(),
    closed: Promise.resolve(),
    drain: vi.fn(),
    error: vi.fn(),
    isClosed: vi.fn(() => false),
    nak: vi.fn(),
    next: vi.fn(),
    unsubscribe: vi.fn(),
  })),
};

// Mock NATS connection
vi.mock('nats', () => ({
  connect: vi.fn(() => ({
    close: vi.fn(),
    closed: vi.fn(() => Promise.resolve()),
    drain: vi.fn(),
    flush: vi.fn(),
    info: vi.fn(() => ({ jetstream: true })),
    jetstream: vi.fn(() => mockJetStreamClient),
    jetstreamManager: vi.fn(() => ({
      streams: {
        add: vi.fn(),
        get: vi.fn(),
        info: vi.fn(),
        update: vi.fn(),
      },
    })),
    publish: vi.fn(),
    request: vi.fn(),
    status: vi.fn(() => 'connected'),
    subscribe: vi.fn(() => ({
      closed: Promise.resolve(),
      drain: vi.fn(),
      error: vi.fn(),
      isClosed: vi.fn(() => false),
      next: vi.fn(),
      unsubscribe: vi.fn(),
      [Symbol.asyncIterator]: async function* () {
        // Mock async iterator for message processing
      },
    })),
  })),
  StringCodec: vi.fn(() => ({
    encode: vi.fn((str) => new TextEncoder().encode(str)),
    decode: vi.fn((data) => new TextDecoder().decode(data)),
  })),
  JSONCodec: vi.fn(() => ({
    encode: vi.fn((obj) => new TextEncoder().encode(JSON.stringify(obj))),
    decode: vi.fn((data) => JSON.parse(new TextDecoder().decode(data))),
  })),
  headers: vi.fn(() => ({
    set: vi.fn(),
    get: vi.fn(),
    has: vi.fn(),
    delete: vi.fn(),
  })),
  RetentionPolicy: {
    Limits: 'limits',
    Interest: 'interest',
    Workqueue: 'workqueue',
  },
  StorageType: {
    File: 'file',
    Memory: 'memory',
  },
}));

// Mock Prisma Client for testing
const mockPrismaClient = {
  $transaction: vi.fn((callback) => callback(mockPrismaClient)), // biome-ignore lint/style/useNamingConvention: Prisma Client uses $transaction
  transactionalOutbox: {
    create: vi.fn(),
    createMany: vi.fn(),
    findMany: vi.fn(),
    updateMany: vi.fn(),
    update: vi.fn(),
    deleteMany: vi.fn(),
    count: vi.fn(),
    aggregate: vi.fn(),
  },
};

describe('EventPublisher', () => {
  let publisher: EventPublisher;

  beforeEach(() => {
    publisher = new EventPublisher({
      serviceName: 'test-publisher',
      stream: {
        name: 'APPOINTMENT_EVENTS',
        subjects: ['appointment.events.*'],
        description: 'All appointment lifecycle events',
      },
    });
    vi.clearAllMocks();
  });

  it('should connect to NATS', async () => {
    await publisher.connect();
    expect(connect).toHaveBeenCalled();
  });

  it('should publish an event', async () => {
    await publisher.connect();
    const mockEvent = createEvent()
      .type(EventTypes.created('test'))
      .aggregate(uuidv4(), 'test')
      .data({ foo: 'bar' })
      .source('test-source')
      .build();

    await publisher.publish(mockEvent);
    expect(mockJetStreamClient.publish).toHaveBeenCalledWith(
      'test.events.test.created',
      expect.any(Uint8Array),
      expect.any(Object),
    );
  });

  it('should disconnect from NATS', async () => {
    await publisher.connect();
    await publisher.disconnect();
    const natsConnection = (connect as any).mock.results[0].value;
    expect(natsConnection.close).toHaveBeenCalled();
  });
});

describe('EventSubscriber', () => {
  let subscriber: EventSubscriber;

  beforeEach(() => {
    subscriber = new EventSubscriber({
      serviceName: 'test-subscriber',
      consumer: {
        name: 'test-subscriber-consumer',
        ackPolicy: 'explicit',
        deliverPolicy: 'new',
      },
    });
    vi.clearAllMocks();
  });

  it('should connect to NATS', async () => {
    await subscriber.connect();
    expect(connect).toHaveBeenCalled();
  });

  it('should subscribe to a stream and process messages', async () => {
    await subscriber.connect();
    const mockCallback = vi.fn();
    const subject = 'appointment.events.created';

    // Simulate a message being received
    const mockMsg = {
      ack: vi.fn(),
      data: new TextEncoder().encode(
        JSON.stringify({ data: { foo: 'bar' }, eventType: 'test.created' }),
      ),
      nak: vi.fn(),
    };

    // Mock the subscribe method to immediately call the callback with a mock message
    const natsConnection = (connect as any).mock.results[0].value;
    natsConnection.subscribe.mockImplementation((_subj: string) => {
      // Simulate async message processing
      setTimeout(() => mockCallback(mockMsg), 0);
      return {
        ack: vi.fn(),
        closed: Promise.resolve(),
        drain: vi.fn(),
        error: vi.fn(),
        isClosed: vi.fn(() => false),
        nak: vi.fn(),
        next: vi.fn(),
        unsubscribe: vi.fn(),
        [Symbol.asyncIterator]: async function* () {
          yield mockMsg;
        },
      };
    });

    await subscriber.subscribe(subject, mockCallback);

    // Wait for the async callback to potentially execute
    await vi.waitFor(() => {
      expect(mockCallback).toHaveBeenCalledWith(mockMsg);
    });
  });

  it('should disconnect from NATS', async () => {
    await subscriber.connect();
    await subscriber.disconnect();
    const natsConnection = (connect as any).mock.results[0].value;
    expect(natsConnection.close).toHaveBeenCalled();
  });
});
