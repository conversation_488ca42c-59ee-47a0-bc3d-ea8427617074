{"author": "Beauty CRM Platform Team", "bin": {"computing-lifecycle": "./dist/bin/cli.js"}, "dependencies": {"@hono/node-server": "^1.19.7", "autoprefixer": "^10.4.23", "cdktf": "^0.21.0", "chalk": "^5.6.2", "commander": "^14.0.2", "constructs": "^10.4.4", "dotenv": "^17.2.3", "hono": "^4.11.3", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "tailwindcss": "^4.1.18", "vite": "^6.4.1", "winston": "^3.19.0"}, "description": "Platform engineering tooling for managing application lifecycle, deployment, and infrastructure automation", "devDependencies": {"@types/node": "^25.0.3", "@vitest/coverage-v8": "^4.0.16", "rimraf": "^6.1.2", "typescript": "^5.9.3", "vitest": "^4.0.6"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-computing-lifecycle", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc --build", "check": "biome check --write .", "check:circular": "madge --circular --extensions ts .", "check:dependencies": "madge --image dependencies.svg --extensions ts ./services ./shared-packages", "clean": "<PERSON><PERSON><PERSON> dist", "deploy:dev": "flyctl deploy", "deploy:prod": "flyctl deploy", "dev": "tsc --watch", "format": "biome format --write --fix .", "infra:deploy": "cdktf deploy", "infra:destroy": "cdktf destroy", "infra:get": "cdktf get", "infra:init": "cdktf init", "infra:plan": "cdktf plan", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "outdated": "bun outdated", "postbuild": "chmod +x dist/bin/cli.js && npm link", "publish": "npm publish --registry http://verdaccio.localhost:4873 --force", "test": "nx vite:test", "test:coverage": "nx vite:test run --coverage", "test:watch": "nx vite:test"}, "types": "dist/index.d.ts", "version": "1.0.1"}