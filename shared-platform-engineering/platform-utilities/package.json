{"author": "Beauty CRM Platform Team", "description": "Shared utilities for Beauty CRM", "devDependencies": {"@types/node": "^25.0.3", "rimraf": "^6.1.2", "typescript": "^5.9.3", "vitest": "^4.0.6"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-utilities", "publishConfig": {"access": "public"}, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc --build", "clean": "rimraf dist .tsbuildinfo", "dev": "tsc --build --watch", "format": "biome format --write . --config-path ../../biome.json", "lint": "biome lint . --config-path ../../biome.json", "lint:check": "biome check . --config-path ../../biome.json", "lint:fix": "biome lint --write . --config-path ../../biome.json", "publish": "npm publish --registry http://verdaccio.localhost:4873 --force", "test": "vitest --run"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.1"}