{"name": "@beauty-crm/beauty-crm-course", "version": "1.0.0", "description": "Interactive course platform for Node.js developers learning Beauty CRM architecture", "main": "src/index.ts", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "format": "bunx @biomejs/biome format . --write", "lint": "bunx @biomejs/biome lint .", "lint:check": "bunx @biomejs/biome check ."}, "dependencies": {"framer-motion": "^12.24.10", "prismjs": "^1.30.0", "react": "^19.2.3", "react-dom": "^19.2.3", "react-icons": "^5.5.0", "react-router-dom": "^7.12.0", "react-syntax-highlighter": "^16.1.0", "zustand": "^5.0.9"}, "devDependencies": {"@biomejs/biome": "^2.3.11", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.18", "typescript": "^5.9.3", "vite": "^7.3.1", "vitest": "^4.0.6"}}