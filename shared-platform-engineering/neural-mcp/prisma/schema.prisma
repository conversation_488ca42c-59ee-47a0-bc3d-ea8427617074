// Neural Network MCP Database Schema

generator client {
    provider      = "prisma-client-js"
    binaryTargets = ["native", "darwin-arm64"]
}

datasource db {
    provider = "sqlite"
}

// Service registration
model Service {
    id             String   @id
    name           String
    type           String
    version        String
    host           String
    port           Int
    healthCheckUrl String?
    tags           String // Stored as JSON string for SQLite compatibility
    metadata       String? // Stored as JSON string for SQLite compatibility
    registeredAt   DateTime @default(now())
    lastHeartbeat  DateTime @default(now())
    isActive       Boolean  @default(true)
    createdAt      DateTime @default(now())
    updatedAt      DateTime @updatedAt

    // Relations
    events Event[]
    health ServiceHealth[]

    @@index([name])
    @@index([isActive])
    @@map("services")
}

// Service events
model Event {
    id            String   @id @default(uuid())
    timestamp     DateTime
    treatmentId     String?
    serviceName   String?
    type          String
    status        String?
    message       String?
    correlationId String?
    traceId       String?
    userId        String?
    tenantId      String?
    endpoint      String?
    method        String?
    durationMs    BigInt?
    metrics       String? // Stored as JSON string for SQLite compatibility
    context       String? // Stored as JSON string for SQLite compatibility
    data          String? // Stored as JSON string for SQLite compatibility
    createdAt     DateTime @default(now())

    // Relations
    service Service? @relation(fields: [treatmentId], references: [id])

    @@index([treatmentId])
    @@index([timestamp])
    @@index([type])
    @@index([status])
    @@map("events")
}

// Neural network state
model NeuralNetworkState {
    id              String   @id @default("current")
    weights         Bytes // Serialized weights
    biases          Bytes // Serialized biases
    inputSize       Int
    hiddenSize      Int
    outputSize      Int
    version         Int      @default(1)
    trainingEvents  Int      @default(0)
    trainingRuns    Int      @default(0)
    lastTrainingAt  DateTime @default(now())
    predictionsMade Int      @default(0)
    createdAt       DateTime @default(now())
    updatedAt       DateTime @updatedAt

    @@map("neural_network_state")
}

// Issue predictions
model Prediction {
    id               String    @id @default(uuid())
    issueType        String
    confidence       Float
    affectedServices String // Stored as JSON string for SQLite compatibility
    suggestedActions String // Stored as JSON string for SQLite compatibility
    relatedEventIds  String // Stored as JSON string for SQLite compatibility
    isResolved       Boolean   @default(false)
    resolvedAt       DateTime?
    createdAt        DateTime  @default(now())
    updatedAt        DateTime  @updatedAt

    @@index([issueType])
    @@index([isResolved])
    @@map("predictions")
}

// Service health records
model ServiceHealth {
    id            String   @id @default(uuid())
    treatmentId     String
    status        String
    metrics       String // Stored as JSON string for SQLite compatibility
    lastCheckedAt DateTime @default(now())
    issues        String? // Stored as JSON string for SQLite compatibility
    createdAt     DateTime @default(now())

    // Relations
    service Service @relation(fields: [treatmentId], references: [id])

    @@index([treatmentId])
    @@index([status])
    @@map("service_health")
}

// Resource recommendations
model ResourceRecommendation {
    id               String    @id @default(uuid())
    treatmentId        String
    resourceType     String
    currentValue     Float
    recommendedValue Float
    priority         String
    justification    String
    estimatedImpact  String // Stored as JSON string for SQLite compatibility
    isApplied        Boolean   @default(false)
    appliedAt        DateTime?
    createdAt        DateTime  @default(now())
    updatedAt        DateTime  @updatedAt

    @@index([treatmentId])
    @@index([resourceType])
    @@index([isApplied])
    @@map("resource_recommendations")
}
