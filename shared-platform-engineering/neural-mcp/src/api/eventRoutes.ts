/**
 * Event processing and analysis routes
 */

// Re-import the actual values
import { z<PERSON><PERSON><PERSON><PERSON> as zodValidator } from '@hono/zod-validator';
import { PrismaClient } from '@prisma/client';
import { Hono } from 'hono';
import { z } from 'zod';
import type { MCPNeuralNetwork } from '../models/neuralNetwork';
import type { ApiResponse, EventResponse } from '../types/types';

// Initialize Prisma client
const prisma = new PrismaClient();

// Event submission validation schema
const eventSchema = z.object({
  context: z.record(z.string(), z.unknown()).optional(),
  correlationId: z.string().optional(),
  data: z.unknown().optional(),
  durationMs: z.number().optional(),
  endpoint: z.string().optional(),
  id: z.string().optional(),
  message: z.string().optional(),
  method: z.string().optional(),
  metrics: z
    .object({
      connectionCount: z.number().optional(),
      cpuUsage: z.number().optional(),
      diskUsage: z.number().optional(),
      errorCount: z.number().optional(),
      memoryUsage: z.number().optional(),
      networkBandwidth: z.number().optional(),
      queueLength: z.number().optional(),
      requestCount: z.number().optional(),
      responseTimeMs: z.number().optional(),
    })
    .optional(),
  serviceId: z.string().optional(),
  serviceName: z.string().optional(),
  status: z.enum(['success', 'error', 'warning', 'info']).optional(),
  tenantId: z.string().optional(),
  timestamp: z
    .number()
    .int()
    .positive()
    .default(() => Date.now()),
  traceId: z.string().optional(),
  type: z.string().min(1, { message: 'Event type is required' }),
  userId: z.string().optional(),
});

const batchEventsSchema = z.object({
  events: z.array(eventSchema).min(1).max(1000),
  sourceId: z.string().optional(),
});

// Create a router
const app = new Hono();

// Reference to neural network instance (injected during server startup)
let neuralNetwork: MCPNeuralNetwork;

// Set neural network instance reference
export const setNeuralNetwork = (nn: MCPNeuralNetwork) => {
  neuralNetwork = nn;
};

/**
 * Process a single event
 */
app.post('/', zodValidator('json', eventSchema), async (c) => {
  const event = c.req.valid('json');

  // Ensure required fields
  if (!event.id) {
    event.id = crypto.randomUUID();
  }

  // Process event
  try {
    if (!neuralNetwork) {
      throw new Error('Neural network not initialized');
    }

    // Record event in neural network
    neuralNetwork.recordEvent(event);

    // Store event in the database
    await prisma.event.create({
      data: {
        context: event.context ? JSON.stringify(event.context) : null,
        correlationId: event.correlationId,
        data: event.data ? JSON.stringify(event.data) : null,
        durationMs: event.durationMs,
        endpoint: event.endpoint,
        id: event.id,
        message: event.message,
        method: event.method,
        metrics: event.metrics ? JSON.stringify(event.metrics) : null,
        serviceId: event.serviceId,
        serviceName: event.serviceName,
        status: event.status,
        tenantId: event.tenantId,
        timestamp: new Date(event.timestamp),
        traceId: event.traceId,
        type: event.type,
        userId: event.userId,
      },
    });

    // Return success response
    const response: ApiResponse<EventResponse> = {
      data: {
        id: event.id as string,
        received: true,
        timestamp: Date.now(),
      },
      success: true,
      timestamp: Date.now(),
    };

    return c.json(response, 201);
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error processing event:', message);

    const response: ApiResponse<null> = {
      error: {
        code: 'EVENT_PROCESSING_ERROR',
        message,
      },
      success: false,
      timestamp: Date.now(),
    };

    return c.json(response, 500);
  }
});

/**
 * Process multiple events in batch
 */
app.post('/batch', zodValidator('json', batchEventsSchema), async (c) => {
  const { events, sourceId } = c.req.valid('json');

  console.log(
    `Received batch of ${events.length} events from ${sourceId || 'unknown source'}`,
  );
  console.log(
    `First event type: ${events[0]?.type}, status: ${events[0]?.status}`,
  );

  // Process events
  try {
    if (!neuralNetwork) {
      throw new Error('Neural network not initialized');
    }

    // Record events in neural network
    const processedEvents: string[] = [];

    for (const event of events) {
      if (!event.id) {
        event.id = crypto.randomUUID();
      }

      neuralNetwork.recordEvent(event);

      try {
        // Store event in database with minimal required fields
        console.log(
          `Storing event ${event.id} of type ${event.type} in database`,
        );

        // Ensure timestamp is a valid Date
        const timestamp = new Date(event.timestamp);
        console.log(`Event timestamp: ${timestamp.toISOString()}`);

        // Log the database operation we're about to perform
        console.log(
          `Database operation: prisma.event.create with id=${event.id}, type=${event.type}`,
        );

        const dbResult = await prisma.event.create({
          data: {
            context: event.context ? JSON.stringify(event.context) : null,
            correlationId: event.correlationId || null,
            data: event.data ? JSON.stringify(event.data) : null,
            durationMs: event.durationMs || 0,
            endpoint: event.endpoint || '',
            id: event.id,
            message: event.message || '',
            method: event.method || null,
            metrics: event.metrics ? JSON.stringify(event.metrics) : null,
            // Only include other fields if they exist
            serviceId: event.serviceId || null,
            serviceName: event.serviceName || null,
            status: event.status || 'info',
            tenantId: event.tenantId || null,
            timestamp: timestamp,
            traceId: event.traceId || null,
            type: event.type,
            userId: event.userId || null,
          },
        });

        console.log(
          `Successfully stored event ${event.id} in database with result:`,
          dbResult.id,
        );
      } catch (dbError) {
        console.error('Error storing event in database:', dbError);
        // Log more details about the error
        if (dbError instanceof Error) {
          console.error(
            `Error name: ${dbError.name}, message: ${dbError.message}`,
          );
          console.error(`Error stack: ${dbError.stack}`);
        }
        // Continue processing other events even if one fails
      }

      processedEvents.push(event.id as string);
    }

    // Return success response
    const response: ApiResponse<{
      processed: number;
      events: string[];
      source: string | undefined;
    }> = {
      data: {
        events: processedEvents,
        processed: events.length,
        source: sourceId,
      },
      success: true,
      timestamp: Date.now(),
    };

    return c.json(response, 201);
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error processing batch events:', message);

    const response: ApiResponse<null> = {
      error: {
        code: 'BATCH_PROCESSING_ERROR',
        message,
      },
      success: false,
      timestamp: Date.now(),
    };

    return c.json(response, 500);
  }
});

export { app as eventRoutes };
