/**
 * Service registration and management routes
 */

import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';
import type { ApiResponse } from '../types/types';

// Service registration schema
const serviceSchema = z.object({
  endpoints: z.array(z.string()).optional(),
  hostname: z.string().optional(),
  ipAddress: z.string().optional(),
  lastHeartbeat: z.number().default(() => Date.now()),
  metadata: z.record(z.string(), z.unknown()).optional(),
  port: z.number().optional(),
  serviceId: z.string().min(1),
  serviceName: z.string().min(1),
  status: z.enum(['online', 'degraded', 'offline']).default('online'),
  version: z.string().optional(),
});

// Create a router
const app = new Hono();

// In-memory service registry (would use database in production)
const services: Record<string, Record<string, unknown>> = {};

/**
 * Register a service
 */
app.post('/', zValidator('json', serviceSchema), async (c) => {
  const service = c.req.valid('json');

  // Register service
  services[service.serviceId] = {
    ...service,
    registeredAt: Date.now(),
  };

  console.log(
    `Service registered: ${service.serviceName} (${service.serviceId})`,
  );

  const response: ApiResponse<{ registered: boolean }> = {
    data: {
      registered: true,
    },
    success: true,
    timestamp: Date.now(),
  };

  return c.json(response, 201);
});

/**
 * Update service status and heartbeat
 */
app.put('/:serviceId', async (c) => {
  const serviceId = c.req.param('serviceId');
  const body = await c.req.json();

  if (!services[serviceId]) {
    const response: ApiResponse<null> = {
      error: {
        code: 'SERVICE_NOT_FOUND',
        message: `Service with ID ${serviceId} not found`,
      },
      success: false,
      timestamp: Date.now(),
    };

    return c.json(response, 404);
  }

  // Update service
  services[serviceId] = {
    ...services[serviceId],
    ...body,
    lastHeartbeat: Date.now(),
  };

  const response: ApiResponse<{ updated: boolean }> = {
    data: {
      updated: true,
    },
    success: true,
    timestamp: Date.now(),
  };

  return c.json(response);
});

/**
 * Get all registered services
 */
app.get('/', async (c) => {
  const serviceList = Object.values(services);

  const response: ApiResponse<Record<string, unknown>[]> = {
    data: serviceList,
    success: true,
    timestamp: Date.now(),
  };

  return c.json(response);
});

/**
 * Get a specific service
 */
app.get('/:serviceId', async (c) => {
  const serviceId = c.req.param('serviceId');

  if (!services[serviceId]) {
    const response: ApiResponse<null> = {
      error: {
        code: 'SERVICE_NOT_FOUND',
        message: `Service with ID ${serviceId} not found`,
      },
      success: false,
      timestamp: Date.now(),
    };

    return c.json(response, 404);
  }

  const response: ApiResponse<Record<string, unknown>> = {
    data: services[serviceId],
    success: true,
    timestamp: Date.now(),
  };

  return c.json(response);
});

/**
 * Deregister a service
 */
app.delete('/:serviceId', async (c) => {
  const serviceId = c.req.param('serviceId');

  if (!services[serviceId]) {
    const response: ApiResponse<null> = {
      error: {
        code: 'SERVICE_NOT_FOUND',
        message: `Service with ID ${serviceId} not found`,
      },
      success: false,
      timestamp: Date.now(),
    };

    return c.json(response, 404);
  }

  // Remove service
  delete services[serviceId];

  const response: ApiResponse<{ deregistered: boolean }> = {
    data: {
      deregistered: true,
    },
    success: true,
    timestamp: Date.now(),
  };

  return c.json(response);
});

export { app as serviceRoutes };
