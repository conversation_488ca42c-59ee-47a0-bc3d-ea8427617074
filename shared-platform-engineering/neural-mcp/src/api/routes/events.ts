import { z<PERSON>alida<PERSON> } from '@hono/zod-validator';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { z } from 'zod';

const app = new Hono();

// Enable CORS for browser extensions
app.use(
  '*',
  cors({
    allowHeaders: ['Content-Type', 'Authorization'],
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    exposeHeaders: ['Content-Length', 'Content-Type'],
    origin: '*',
  }),
);

// Schema for single event
const EventSchema = z.object({
  context: z.record(z.string(), z.unknown()).optional(),
  durationMs: z.number().optional(),
  endpoint: z.string().optional(),
  id: z.string().optional(),
  message: z.string().optional(),
  method: z.string().optional(),
  metrics: z.record(z.string(), z.unknown()).optional(),
  status: z.string().optional(),
  timestamp: z.number().optional(),
  type: z.string(),
});

// Schema for batch events request
const BatchEventRequestSchema = z.object({
  events: z.array(EventSchema),
  sourceId: z.string().optional(),
});

// Process a single event
app.post('/', zValidator('json', EventSchema), async (c) => {
  const event = c.req.valid('json');

  // Add timestamp if not provided
  if (!event.timestamp) {
    event.timestamp = Date.now();
  }

  // Add ID if not provided
  if (!event.id) {
    event.id = crypto.randomUUID();
  }

  console.log(`Received event: ${event.type}`);

  // Here you would typically process the event,
  // e.g. store in database, analyze with neural network, etc.

  return c.json({
    data: {
      eventId: event.id,
      processed: 1,
    },
    success: true,
  });
});

// Process batch events
app.post('/batch', zValidator('json', BatchEventRequestSchema), async (c) => {
  const { events, sourceId } = c.req.valid('json');

  console.log(
    `Received batch of ${events.length} events from ${sourceId || 'unknown source'}`,
  );

  // Process each event - in a real implementation, you would
  // likely batch insert these to a database
  const processedEvents = events.map((event) => {
    // Add timestamp if not provided
    if (!event.timestamp) {
      event.timestamp = Date.now();
    }

    // Add ID if not provided
    if (!event.id) {
      event.id = crypto.randomUUID();
    }

    return event;
  });

  // Here you would typically process the events with the neural network

  return c.json({
    data: {
      processed: processedEvents.length,
      source: sourceId,
    },
    success: true,
  });
});

export default app;
