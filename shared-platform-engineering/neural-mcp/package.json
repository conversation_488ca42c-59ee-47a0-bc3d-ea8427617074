{"dependencies": {"@hono/node-server": "^1.19.7", "@prisma/client": "^7.2.0", "hono": "^4.11.3", "ioredis": "^5.9.0", "zod": "^4.3.5"}, "description": "Neural Network Master Control Program for Beauty CRM Services", "devDependencies": {"@types/node": "^25.0.3", "prisma": "^7.2.0", "tsx": "^4.21.0", "typescript": "^5.9.3", "vitest": "^4.0.6"}, "engines": {"node": ">=18.0.0"}, "main": "dist/index.js", "name": "@beauty-crm/neural-network-mcp", "scripts": {"build": "tsc", "db:generate": "prisma generate", "db:migrate": "prisma migrate deploy", "db:studio": "prisma studio", "dev": "tsx watch src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prestart": "npm run build", "start": "node dist/index.js", "test": "vitest run", "test:watch": "vitest"}, "types": "dist/index.d.ts", "version": "1.0.0"}