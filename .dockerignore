# Git files
.git
.gitignore

# Node / Bun
node_modules
**/node_modules
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
bun.lockb # If you are only using bun.lock

# Build artifacts
dist
build
out

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE / Editor folders
.vscode
.idea
*.swp
*~

# Environment files - should be passed via build args or runtime env vars
.env
.env.*
!/.env.example

# Docker specific
Dockerfile # If Dockerfiles are not meant to be part of other images' context
docker-compose.yml
docker-compose.*.yml

# Temporary files and caches
tmp/
cache/
.cache/

# Specific to this project structure if they exist at root and are not needed in context
# Add any other top-level directories or files that should be excluded
# e.g. if you have coverage reports or local test databases
coverage/
.nyc_output/

# Ignore everything first
*
**/node_modules
**/.git
**/.next
**/.nuxt
**/.output
**/.cache
**/.tmp
**/.DS_Store
**/.env*
**/*.log
**/.idea
**/.vscode
**/*.swp
**/*.swo
**/coverage
**/test-results
**/playwright-report
**/dist
**/build
**/*.md
**/*.adoc
**/*.txt
**/*.yaml
**/*.yml
**/*.json
**/*.lock
**/*.config.js
**/*.config.ts
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx
**/*.stories.tsx
**/*.d.ts
**/__tests__
**/__mocks__
**/tests
**/e2e
**/cucumber
**/docs
**/assets
**/public

# Then explicitly include what we need
!package.json
!bun.lock
!bunfig.toml
!tsconfig.base.json
!tsconfig.json
!biome.json
.biome/
# Include only the necessary source files for each service
!services/appointment/appointment-planner-frontend/src/
!services/appointment/appointment-planner-frontend/package.json
!services/appointment/appointment-planner-frontend/tsconfig.json

!services/appointment/appointment-planner-backend/src/
!services/appointment/appointment-planner-backend/package.json
!services/appointment/appointment-planner-backend/tsconfig.json
!services/appointment/appointment-planner-backend/prisma/
!services/appointment/appointment-planner-backend/prisma.config.ts

!services/appointment/appointment-management-backend/src/
!services/appointment/appointment-management-backend/package.json
!services/appointment/appointment-management-backend/tsconfig.json
!services/appointment/appointment-management-backend/prisma/
!services/appointment/appointment-management-backend/prisma.config.ts

# Include only necessary shared code
!shared-platform-engineering/platform-introvertic-ui/src/
!shared-platform-engineering/platform-introvertic-ui/package.json

!shared-product-engineering/product-domain-types/src/
!shared-product-engineering/product-domain-types/package.json

!shared-ddd-layers/domain/src/
!shared-ddd-layers/domain/package.json
!shared-ddd-layers/application/src/
!shared-ddd-layers/application/package.json
!shared-ddd-layers/infrastructure/src/
!shared-ddd-layers/infrastructure/package.json
!shared-ddd-layers/presentation/src/
!shared-ddd-layers/presentation/package.json

# Allow specific files and directories
!package.json
!bun.lock
!bunfig.toml
!tsconfig.base.json
!shared-platform-engineering/*/package.json
!shared-product-engineering/*/package.json
!shared-ddd-layers/*/package.json
!services/appointment/*/package.json
!services/salon/*/package.json
!services/public-identity/*/package.json
!services/staff/*/package.json

# Allow source files for copying
!shared-platform-engineering/
!shared-product-engineering/
!shared-ddd-layers/
!services/appointment/
!services/salon/
!services/public-identity/
!services/staff/

# Ignore node_modules everywhere
**/node_modules/
**/dist/
**/.git/
**/.DS_Store
**/*.log
**/coverage/
**/.env

# Allow dist directories for appointment services
!services/appointment/appointment-planner-frontend/dist/
!services/appointment/appointment-management-frontend/dist/
!services/appointment/appointment-planner-backend/dist/
!services/appointment/appointment-management-backend/dist/

# Allow dist directories for treatment services
!services/treatment/treatment-management-frontend/dist/
!services/treatment/treatment-management-backend/dist/

# Allow public-identity service files
!services/public-identity/public-identity-management-backend/src/
!services/public-identity/public-identity-management-backend/package.json
!services/public-identity/public-identity-management-backend/tsconfig.json
!services/public-identity/public-identity-management-backend/prisma/
!services/public-identity/public-identity-management-backend/prisma.config.ts
!services/public-identity/public-identity-management-backend/dist/

!services/public-identity/public-identity-management-frontend/src/
!services/public-identity/public-identity-management-frontend/package.json
!services/public-identity/public-identity-management-frontend/tsconfig.json
!services/public-identity/public-identity-management-frontend/dist/

# Allow staff service files
!services/staff/staff-management-backend/src/
!services/staff/staff-management-backend/package.json
!services/staff/staff-management-backend/tsconfig.json
!services/staff/staff-management-backend/prisma/
!services/staff/staff-management-backend/prisma.config.ts
!services/staff/staff-management-backend/dist/

!services/staff/staff-management-frontend/src/
!services/staff/staff-management-frontend/package.json
!services/staff/staff-management-frontend/tsconfig.json
!services/staff/staff-management-frontend/dist/

# Allow salon service files
!services/salon/salon-management-backend/src/
!services/salon/salon-management-backend/package.json
!services/salon/salon-management-backend/tsconfig.json
!services/salon/salon-management-backend/prisma/
!services/salon/salon-management-backend/prisma.config.ts
!services/salon/salon-management-backend/dist/

!services/salon/salon-management-frontend/src/
!services/salon/salon-management-frontend/package.json
!services/salon/salon-management-frontend/tsconfig.json
!services/salon/salon-management-frontend/dist/

# Allow treatment service files
!services/treatment/treatment-management-backend/src/
!services/treatment/treatment-management-backend/package.json
!services/treatment/treatment-management-backend/tsconfig.json
!services/treatment/treatment-management-backend/prisma/
!services/treatment/treatment-management-backend/prisma.config.ts
!services/treatment/treatment-management-backend/dist/

# Allow planner service files
!services/planner/planner-management-backend/src/
!services/planner/planner-management-backend/package.json
!services/planner/planner-management-backend/tsconfig.json
!services/planner/planner-management-backend/prisma/
!services/planner/planner-management-backend/prisma.config.ts
!services/planner/planner-management-backend/dist/


# Allow dist directories for shared libraries using wildcards
!shared-platform-engineering/*/dist/
!shared-product-engineering/*/dist/


# Allow nginx configuration files
!services/treatment/treatment-management-frontend/nginx.conf