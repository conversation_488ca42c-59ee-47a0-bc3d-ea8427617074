{"dependencies": {"@beauty-crm/platform-appointment-unified": "^1.0.0", "@beauty-crm/platform-computing-lifecycle": "^1.0.0", "@beauty-crm/platform-eventing": "^1.0.0", "@hono/node-server": "^1.19.7", "@hono/zod-validator": "^0.7.6", "@paralleldrive/cuid2": "^3.0.6", "@prisma/client": "^7.2.0", "axios": "^1.13.2", "hono": "^4.11.3", "i18next": "^25.7.4", "nodemailer": "^7.0.12", "zod": "^4.3.5"}, "description": "Appointment Planner Backend Appointment API and Available Slots API synchronised with appointment backend", "devDependencies": {"@cucumber/cucumber": "^12.5.0", "@playwright/test": "^1.57.0", "@types/chai": "^5.2.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^25.0.3", "@types/nodemailer": "^7.0.4", "@types/supertest": "^6.0.3", "@vitest/coverage-v8": "^4.0.16", "@vitest/ui": "4.0.16", "bun-types": "^1.3.1", "chai": "^6.2.2", "prisma": "^7.2.0", "smtp-tester": "^2.1.0", "supertest": "^7.2.2", "ts-node": "^10.9.2", "tsx": "^4.21.0", "typescript": "^5.9.3", "vite": "^7.3.1", "vite-plugin-node": "^5.0.1", "vitest": "^4.0.6"}, "files": ["dist"], "keywords": ["appointment", "planner", "appointment", "beauty"], "main": "dist/index.js", "name": "@beauty-crm/appointment-planner-backend", "prisma": {"packageManager": "bun"}, "scripts": {"build": "bun x tsc --project tsconfig.json && mkdir -p dist", "build:test": "tsc --project tsconfig.test.json", "dev": "DATABASE_URL=*********************************************************/beauty_crm PORT=5016 bun --hot src/index.ts", "prisma:generate": "bunx prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:migrate:reset": "prisma migrate reset", "prisma:push": "prisma db push", "prisma:studio": "prisma studio", "start": "bun run dist/index.js", "worker": "bun run src/infrastructure/outbox-worker.ts", "test:cucumber": "concurrently \"bun run test:cucumber:double-appointment\" \"bun run test:cucumber:double-appointment-bug\" \"bun run test:cucumber:email\" \"bun run test:cucumber:cancellation\" \"bun run test:cucumber:fully-booked\" \"bun run test:cucumber:slow-appointment\" \"bun run test:cucumber:amsterdam\" \"bun run test:cucumber:breda\" \"bun run test:cucumber:newyork\"", "test:cucumber:amsterdam": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/timezone-amsterdam/amsterdam-appointment.feature --import src/tests/features/timezone-amsterdam/amsterdam-appointment.steps.ts", "test:cucumber:breda": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/timezone-breda/breda-appointment.feature --import src/tests/features/timezone-breda/breda-appointment.steps.ts", "test:cucumber:cancellation": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/cancellation/cancellation.feature --import src/tests/features/cancellation/cancellation.steps.ts", "test:cucumber:double-appointment": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/double-appointment/double-appointment.feature --import src/tests/features/double-appointment/double-appointment.steps.ts", "test:cucumber:double-appointment-bug": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/double-appointment/double-appointment.feature --import src/tests/features/double-appointment/double-appointment-bug.steps.ts", "test:cucumber:email": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/email/email.feature --import src/tests/features/email/email.steps.ts", "test:cucumber:fully-booked": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/fully-booked/fully-booked.feature --import src/tests/features/fully-booked/fully-booked.steps.ts", "test:cucumber:newyork": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/timezone-newyork/newyork-appointment.feature --import src/tests/features/timezone-newyork/newyork-appointment.steps.ts", "test:cucumber:slow-appointment": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/slow-appointment/slow-appointment.feature --import src/tests/features/slow-appointment/slow-appointment.steps.ts", "test:cucumber:sprint6": "concurrently \"bun run test:cucumber:sprint6-salon-registration\" \"bun run test:cucumber:sprint6-appointment-booking\"", "test:cucumber:sprint6-appointment-booking": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/netherlands-market/dutch-appointment-booking.feature --import src/tests/features/netherlands-market/dutch-appointment-booking.steps.ts", "test:cucumber:sprint6-salon-registration": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/netherlands-market/dutch-salon-registration.feature --import src/tests/features/netherlands-market/dutch-salon-registration.steps.ts", "test:service:email": "bun test src/infrastructure/services/emailService.test.ts", "test:api": "vitest run src/tests/api/", "test:api:watch": "vitest watch src/tests/api/", "test:coverage": "vitest run --coverage", "test:coverage:api": "vitest run --coverage src/tests/api/", "test:coverage:html": "vitest run --coverage --reporter=html", "test:coverage:threshold": "vitest run --coverage --coverage.thresholds.lines=70 --coverage.thresholds.functions=70 --coverage.thresholds.branches=70 --coverage.thresholds.statements=70", "test:supertest": "vitest run src/tests/api/supertest-integration.test.ts", "test:supertest:watch": "vitest watch src/tests/api/supertest-integration.test.ts", "test:unit": "vitest run src/tests/unit/", "test:unit:watch": "vitest watch src/tests/unit/", "test:all": "vitest run src/tests/", "test:all:watch": "vitest watch src/tests/"}, "type": "module", "version": "1.0.0"}