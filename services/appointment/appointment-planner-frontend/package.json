{"dependencies": {"@beauty-crm/platform-computing-lifecycle": "^1.0.0", "@beauty-crm/platform-environment-names": "^1.0.0", "@beauty-crm/platform-identity-client": "^1.0.0", "@beauty-crm/platform-introvertic-ui": "^1.0.0", "@beauty-crm/platform-logger": "^1.0.3", "@beauty-crm/platform-utilities": "^1.0.0", "@beauty-crm/product-domain-types": "^1.0.0", "@beauty-crm/product-identity-types": "^1.0.0", "@beauty-crm/product-kernel": "^1.0.0", "@hookform/resolvers": "^5.2.2", "@tanstack/react-query": "^5.90.16", "axios": "^1.13.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.562.0", "msw": "^2.12.7", "react": "^19.2.3", "react-dom": "^19.2.3", "react-hook-form": "^7.70.0", "react-router-dom": "^7.12.0", "zod": "^4.3.5"}, "description": "Appointment Planner Frontend", "devDependencies": {"@testing-library/jest-dom": "^6.9.1", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^5.1.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "supertest": "^7.2.2", "typescript": "^5.9.3", "vite": "^7.3.1", "vitest": "^4.0.6"}, "name": "@beauty-crm/appointment-planner-frontend", "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"build": "vite build", "build:test": "tsc --project tsconfig.test.json", "dev": "vite --host 0.0.0.0", "dev:test": "vite --port ${FRONTEND_PORT:-5016} --host 0.0.0.0", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "vite preview", "test": "vitest run", "test:api": "vitest run src/tests/api/", "test:api:watch": "vitest watch src/tests/api/", "test:ci": "CI=true vitest run", "test:coverage": "vitest run --coverage", "test:coverage:api": "vitest run --coverage src/tests/api/", "test:coverage:html": "vitest run --coverage --reporter=html", "test:coverage:threshold": "vitest run --coverage --coverage.thresholds.lines=70 --coverage.thresholds.functions=70 --coverage.thresholds.branches=70 --coverage.thresholds.statements=70", "test:ui": "vitest --ui", "test:watch": "vitest"}, "type": "module", "version": "1.0.0"}