{"dependencies": {"@beauty-crm/platform-appointment-unified": "^1.0.0", "@beauty-crm/platform-db-client": "^1.0.4", "@beauty-crm/platform-eventing": "^1.0.0", "@beauty-crm/platform-identity-client": "^1.0.0", "@beauty-crm/platform-utilities": "^1.0.0", "@hono/node-server": "^1.19.7", "@prisma/client": "^7.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.3", "date-fns": "^4.1.0", "hono": "^4.11.3", "ioredis": "^5.9.0", "nats": "^2.29.3", "node-schedule": "^2.1.1", "reflect-metadata": "^0.2.2", "zod": "^4.3.5"}, "description": "API service managing appointment operations, business rules, and appointment workflows for Beauty CRM", "devDependencies": {"@types/node": "^25.0.3", "@types/node-schedule": "^2.1.8", "prisma": "^7.2.0", "rimraf": "^6.1.2", "tsx": "^4.21.0", "typescript": "^5.9.3", "vite": "^7.3.1", "vitest": "^4.0.6"}, "name": "@beauty-crm/appointment-management-backend", "private": true, "scripts": {"build": "bun build src/index.ts --outdir dist --target bun", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "bun run --watch src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:seed": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed.ts", "start": "bun run src/index.ts", "start:prod": "bun run dist/index.js", "worker": "bun run src/infrastructure/event-listener.ts", "test": "vitest", "test:coverage": "vitest run --coverage", "tsc": "tsc"}, "type": "module", "version": "1.0.0"}