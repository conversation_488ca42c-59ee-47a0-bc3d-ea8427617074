import type { Context } from 'hono';
import { z } from 'zod';
import type { TreatmentService } from '../application/services/TreatmentService';

// Validation schema for treatment
const treatmentSchema = z.object({
  categoryId: z.string().optional(),
  description: z.string().optional(),
  duration: z.number().min(1, 'Duration must be at least 1 minute'),
  name: z.string().min(1, 'Name is required'),
  price: z.number().min(0, 'Price must be non-negative'),
});

const updateTreatmentSchema = treatmentSchema.partial();

export class TreatmentController {
  constructor(private treatmentService: TreatmentService) {}

  // Get all treatments
  getAll = async (c: Context) => {
    try {
      const { salonId } = c.req.query();
      const treatments = await this.treatmentService.getAllTreatments(
        salonId || 'salon1',
      );
      return c.json({
        count: treatments.length,
        data: treatments,
        success: true,
      });
    } catch (error) {
      console.error('Error getting treatments:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // Get treatment by ID
  getById = async (c: Context) => {
    try {
      const { id } = c.req.param();
      const treatment = await this.treatmentService.getTreatmentById(id);

      if (!treatment) {
        return c.json({ error: 'Treatment not found', success: false }, 404);
      }

      return c.json({ data: treatment, success: true });
    } catch (error) {
      console.error('Error getting treatment by ID:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // Create a new treatment
  create = async (c: Context) => {
    try {
      const body = await c.req.json();

      // Validate input
      const validationResult = treatmentSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid treatment data',
            success: false,
          },
          400,
        );
      }

      const treatmentData = validationResult.data;
      const treatment =
        await this.treatmentService.createTreatment(treatmentData);

      return c.json({ data: treatment, success: true }, 201);
    } catch (error) {
      console.error('Error creating treatment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // Update a treatment
  update = async (c: Context) => {
    try {
      const { id } = c.req.param();
      const body = await c.req.json();

      // Validate input
      const validationResult = updateTreatmentSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid treatment data',
            success: false,
          },
          400,
        );
      }

      const treatment = await this.treatmentService.updateTreatment(
        id,
        validationResult.data,
      );

      if (!treatment) {
        return c.json({ error: 'Treatment not found', success: false }, 404);
      }

      return c.json({ data: treatment, success: true });
    } catch (error) {
      console.error('Error updating treatment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // Delete a treatment
  delete = async (c: Context) => {
    try {
      const { id } = c.req.param();
      const treatment = await this.treatmentService.deleteTreatment(id);

      if (!treatment) {
        return c.json({ error: 'Treatment not found', success: false }, 404);
      }

      return c.json({
        data: treatment,
        message: 'Treatment deleted successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error deleting treatment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // Get treatments by category
  getByCategory = async (c: Context) => {
    try {
      const { categoryId } = c.req.param();
      const treatments =
        await this.treatmentService.getTreatmentsByCategory(categoryId);

      return c.json({
        count: treatments.length,
        data: treatments,
        success: true,
      });
    } catch (error) {
      console.error('Error getting treatments by category:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // Search treatments
  search = async (c: Context) => {
    try {
      const { q } = c.req.query();

      if (!q) {
        return c.json(
          {
            error: 'Search query is required',
            success: false,
          },
          400,
        );
      }

      const treatments = await this.treatmentService.searchTreatments(q);

      return c.json({
        count: treatments.length,
        data: treatments,
        query: q,
        success: true,
      });
    } catch (error) {
      console.error('Error searching treatments:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // Get treatment categories
  getCategories = async (c: Context) => {
    try {
      const categories = await this.treatmentService.getTreatmentCategories();
      return c.json({
        count: categories.length,
        data: categories,
        success: true,
      });
    } catch (error) {
      console.error('Error getting treatment categories:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // Toggle treatment active status
  toggleActive = async (c: Context) => {
    try {
      const { id } = c.req.param();
      const treatment = await this.treatmentService.toggleTreatmentActive(id);

      if (!treatment) {
        return c.json({ error: 'Treatment not found', success: false }, 404);
      }

      return c.json({
        data: treatment,
        message: `Treatment ${treatment.isActive ? 'activated' : 'deactivated'} successfully`,
        success: true,
      });
    } catch (error) {
      console.error('Error toggling treatment active status:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };
}
