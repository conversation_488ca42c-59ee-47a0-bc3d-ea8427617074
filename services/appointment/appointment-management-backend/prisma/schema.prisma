generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64"]
}

datasource db {
  provider = "postgresql"
}

// Treatment models removed - handled by separate treatment service
// TreatmentOffering model removed - treatments are now managed by the Treatment service
// TODO remove customer and use new service customer service
model Appointment {
  @@map("appointments")

  id                   String            @id @default(cuid())
  salonId              String // Salon ID for multi-tenancy
  customerId           String // Renamed to customerId to align with UnifiedAppointment interface
  customerEmail        String?
  customerName         String?
  customerPhone        String?
  staffId              String?
  treatmentId          String // Reference to treatment service via CUID
  startTime            DateTime
  endTime              DateTime
  status               AppointmentStatus @default(PENDING)
  notes                String?
  plannerAppointmentId String? // Track source appointment from planner service
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
}

enum AppointmentStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
  RESCHEDULED
}