import type React from 'react';
import { useState } from 'react';
import { z } from 'zod';

// Define types
interface Treatment {
  id: string;
  name: string;
  durationMinutes: number;
  price: number;
}

interface TimeSlot {
  id: string;
  start: string;
  end: string;
}

interface ClientDetails {
  name: string;
  phone: string;
  email: string;
}

interface Appointment {
  treatmentId: string;
  timeSlotId: string;
  client: ClientDetails;
}

interface AppointmentFormProps {
  availableTreatments: Treatment[];
  availableTimeSlots: TimeSlot[];
  bookedTimeSlots: TimeSlot[];
  onSuccess: (appointment: Appointment) => void;
  // onError: (error: any) => void; // Removed unused prop
}

// Validation schema
const clientSchema = z.object({
  email: z.string().email('Invalid email'),
  name: z.string().min(1, 'Name is required'),
  phone: z.string().regex(/^\d{10}$/, 'Invalid phone number'),
});

export const AppointmentForm: React.FC<AppointmentFormProps> = ({
  availableTreatments,
  availableTimeSlots,
  bookedTimeSlots,
  onSuccess,
}) => {
  const [showForm, setShowForm] = useState(false);
  const [selectedTreatment, setSelectedTreatment] = useState('');
  const [selectedTimeSlot, setSelectedTimeSlot] = useState('');
  const [clientDetails, setClientDetails] = useState<ClientDetails>({
    email: '',
    name: '',
    phone: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [confirmationMessage, setConfirmationMessage] = useState('');
  const [appointmentError, setAppointmentError] = useState('');

  const handleNewAppointment = () => {
    setShowForm(true);
    setErrors({});
    setAppointmentError('');
    setConfirmationMessage('');
  };

  const handleTreatmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedTreatment(e.target.value);
  };

  const handleTimeSlotChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const timeSlotId = e.target.value;
    setSelectedTimeSlot(timeSlotId);

    // Check if time slot is already booked
    const isBooked = bookedTimeSlots.some((slot) => slot.id === timeSlotId);
    if (isBooked) {
      setAppointmentError('Time slot already booked');
    } else {
      setAppointmentError('');
    }
  };

  const handleClientDetailsChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { name, value } = e.target;
    setClientDetails((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = (): boolean => {
    try {
      clientSchema.parse(clientDetails);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedErrors: Record<string, string> = {};
        for (const err of error.issues) {
          if (err.path[0]) {
            formattedErrors[err.path[0].toString()] = err.message;
          }
        }
        setErrors(formattedErrors);
      }
      return false;
    }
  };

  const handleConfirm = () => {
    // Reset errors and messages
    setErrors({});
    setAppointmentError('');
    setConfirmationMessage('');

    // Check if time slot is booked
    const isBooked = bookedTimeSlots.some(
      (slot) => slot.id === selectedTimeSlot,
    );
    if (isBooked) {
      setAppointmentError('Time slot already booked');
      return;
    }

    // Validate client details
    if (!validateForm()) {
      return;
    }

    // Create appointment
    const appointment = {
      client: clientDetails,
      timeSlotId: selectedTimeSlot,
      treatmentId: selectedTreatment,
    };

    // Simulate API call
    setTimeout(() => {
      setConfirmationMessage('Appointment confirmed!');
      onSuccess(appointment);
      // Reset form
      setShowForm(false);
      setSelectedTreatment('');
      setSelectedTimeSlot('');
      setClientDetails({ email: '', name: '', phone: '' });
    }, 500);
  };

  return (
    <div className="appointment-form">
      {!showForm && (
        <button type="button" onClick={handleNewAppointment}>
          New Appointment
        </button>
      )}

      {showForm && (
        <div className="form-container">
          <h2>Create New Appointment</h2>

          <div className="form-group">
            <label htmlFor="treatment">Treatment</label>
            <select
              id="treatment"
              value={selectedTreatment}
              onChange={handleTreatmentChange}
              aria-label="treatment"
            >
              <option value="">Select a treatment</option>
              {availableTreatments.map((treatment) => (
                <option key={treatment.id} value={treatment.id}>
                  {treatment.name} - ${treatment.price} (
                  {treatment.durationMinutes} min)
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="timeSlot">Time Slot</label>
            <select
              id="timeSlot"
              value={selectedTimeSlot}
              onChange={handleTimeSlotChange}
              aria-label="time slot"
            >
              <option value="">Select a time slot</option>
              {availableTimeSlots.map((slot) => (
                <option key={slot.id} value={slot.id}>
                  {new Date(slot.start).toLocaleString()} -{' '}
                  {new Date(slot.end).toLocaleTimeString()}
                </option>
              ))}
            </select>
            {appointmentError && (
              <div className="error">{appointmentError}</div>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              value={clientDetails.name}
              onChange={handleClientDetailsChange}
              aria-label="name"
            />
            {errors.name && <div className="error">{errors.name}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="phone">Phone</label>
            <input
              type="text"
              id="phone"
              name="phone"
              value={clientDetails.phone}
              onChange={handleClientDetailsChange}
              aria-label="phone"
            />
            {errors.phone && <div className="error">{errors.phone}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={clientDetails.email}
              onChange={handleClientDetailsChange}
              aria-label="email"
            />
            {errors.email && <div className="error">{errors.email}</div>}
          </div>

          <button type="button" onClick={handleConfirm}>
            Confirm
          </button>
        </div>
      )}

      {confirmationMessage && (
        <div className="confirmation">{confirmationMessage}</div>
      )}
    </div>
  );
};
