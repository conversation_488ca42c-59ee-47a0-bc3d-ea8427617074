{"dependencies": {"@beauty-crm/platform-computing-lifecycle": "^1.0.0", "@hookform/resolvers": "^5.2.2", "@radix-ui/react-avatar": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.8", "@radix-ui/react-slot": "^1.2.4", "@tanstack/react-query": "^5.90.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "comlink": "^4.4.2", "lucide-react": "^0.562.0", "react": "^19.2.3", "react-dom": "^19.2.3", "react-hook-form": "^7.70.0", "react-router-dom": "^7.12.0", "tailwind-merge": "^3.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^4.3.5"}, "description": "Identity Management Frontend for Beauty CRM", "devDependencies": {"@types/node": "^25.0.3", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@vitejs/plugin-react": "^5.1.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.16", "typescript": "^5.9.3", "vite": "^7.3.1", "vite-plugin-top-level-await": "^1.6.0", "vite-plugin-wasm": "^3.5.0", "vitest": "^4.0.6"}, "name": "@beauty-crm/identity", "private": true, "scripts": {"build": "tsc && nx vite:build", "dev": "platform-shell-lifecycle start", "dev:vite": "nx serve", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "nx vite:preview", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "1.0.0"}