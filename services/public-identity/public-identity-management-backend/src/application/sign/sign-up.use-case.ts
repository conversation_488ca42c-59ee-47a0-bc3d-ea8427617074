import * as crypto from 'node:crypto';
import type {
  SharedSignUpInput,
  SharedSignUpOutput,
} from '@beauty-crm/product-identity-types';
import {
  AUTH_ERROR_CODES,
  userPoolRegistrationSchema,
} from '@beauty-crm/product-identity-types';
import { DomainError } from '@/domain/core/domain.error';
import type { IEmailService } from '@/domain/email/email-service';
import type { IPasswordService } from '@/domain/password/password-service';
import { Password } from '@/domain/password/types';
import type { ISessionRepository } from '@/domain/session/session-repository';
import type { ITokenService } from '@/domain/token/token-service';
import type { IUserRepository } from '@/domain/user/user-repository';

export class SignUpUseCase {
  constructor(
    private readonly userRepository: IUserRepository,
    private readonly passwordService: IPasswordService,
    private readonly emailService: IEmailService,
    private readonly sessionRepository?: ISessionRepository,
    private readonly tokenService?: ITokenService,
  ) {}

  private validateRegistrationContract(
    params: SharedSignUpInput,
  ): SharedSignUpInput {
    // Validate using Zod schema
    const validationResult = userPoolRegistrationSchema.safeParse({
      ...params,
      organizationId: params.organizationId,
      role: params.role,
    });

    if (!validationResult.success) {
      // Throw first validation error
      const firstError = validationResult.error.issues[0];
      throw new DomainError(firstError.message, AUTH_ERROR_CODES.INVALID_INPUT);
    }

    return validationResult.data;
  }

  // Helper method to create a basic JWT token without using the token service
  private createBasicJwtToken(payload: Record<string, unknown>): string {
    try {
      // Create a simple JWT token structure
      const header = { alg: 'HS256', typ: 'JWT' };
      const encodedHeader = Buffer.from(JSON.stringify(header)).toString(
        'base64url',
      );
      const encodedPayload = Buffer.from(JSON.stringify(payload)).toString(
        'base64url',
      );

      // For signature, we'll use a simple hash since we don't have access to the secret
      // This is just for fallback purposes and not secure for production
      const signature = crypto
        .createHash('sha256')
        .update(`${encodedHeader}.${encodedPayload}`)
        .digest('base64url');

      return `${encodedHeader}.${encodedPayload}.${signature}`;
    } catch (error) {
      console.error('[SignUpUseCase] Failed to create basic JWT token:', error);
      return `jwt-fallback-${crypto.randomUUID()}`;
    }
  }

  async execute(params: SharedSignUpInput): Promise<SharedSignUpOutput> {
    // DEBUG LOGGING FOR TOKEN SERVICE
    console.log('[SignUpUseCase] TokenService available:', !!this.tokenService);
    if (this.tokenService) {
      console.log(
        '[SignUpUseCase] TokenService type:',
        this.tokenService.constructor.name,
      );
    }
    // END DEBUG LOGGING

    // Validate input
    const validatedParams = this.validateRegistrationContract({
      ...params,
      organizationId: params.organizationId,
      role: params.role,
    });

    // Create password object
    const passwordObj = Password.create(validatedParams.password);
    if (passwordObj instanceof Error) {
      throw new DomainError(
        passwordObj.message,
        AUTH_ERROR_CODES.INVALID_PASSWORD,
      );
    }

    // Hash password
    const passwordHash = await this.passwordService.hashPassword(passwordObj);

    // Create user metadata
    const metadata = {
      acceptedTerms: validatedParams.acceptedTerms,
      firstName: validatedParams.firstName,
      lastName: validatedParams.lastName,
      marketingConsent: validatedParams.marketingConsent,
      role: validatedParams.role,
    };

    // Check if email domain should be auto-verified
    const emailDomain = validatedParams.email.split('@')[1]?.toLowerCase();
    const autoVerifiedDomains = ['mailinator.com'];
    const shouldAutoVerify = autoVerifiedDomains.includes(emailDomain);

    // Create user with auto-verification if applicable
    const user = await this.userRepository.create({
      email: validatedParams.email,
      emailVerified: shouldAutoVerify,
      metadata: JSON.parse(
        JSON.stringify({
          ...metadata,
          ...(shouldAutoVerify && {
            autoVerified: true,
            autoVerifiedAt: new Date().toISOString(),
            autoVerifiedEnvironment: process.env.NODE_ENV || 'unknown',
            complianceNote:
              'Auto-verified for testing purposes only. Not compliant with SOC 3 or NIST 800-63B in production.',
          }),
        }),
      ),
      passwordHash: passwordHash.getValue(),
      role: validatedParams.role === 'STAFF' ? 'STAFF' : 'USER', // Auto-verify if domain matches
    });

    console.log('[SignUpUseCase] User created successfully:', {
      email: user.email,
      emailVerified: user.emailVerified,
      hasPasswordHash: !!user.passwordHash,
      metadata: JSON.stringify(user.metadata),
      userId: user.id,
    });

    // Create a basic payload for token
    const tokenPayload = {
      iat: Math.floor(Date.now() / 1000),
      sessionId: crypto.randomUUID ? crypto.randomUUID() : user.id,
      userId: user.id,
    };

    let session = {
      accessToken: '',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      id: user.id,
      refreshToken: '', // 24 hours from now
    };

    // Generate tokens using token service if available
    if (this.tokenService) {
      try {
        console.log('[SignUpUseCase] TokenService detected. Generating tokens');
        console.log('[SignUpUseCase] Token payload:', tokenPayload);

        const accessToken = this.tokenService.generateAccessToken(tokenPayload);
        const refreshToken =
          this.tokenService.generateRefreshToken(tokenPayload);

        session.accessToken = accessToken;
        session.refreshToken = refreshToken;

        console.log('[SignUpUseCase] Generated JWT tokens:', {
          refreshTokenEnd: `...${refreshToken.substring(refreshToken.length - 10)}`,
          refreshTokenLength: refreshToken.length,
          refreshTokenStart: `${refreshToken.substring(0, 10)}...`,
          tokenEnd: `...${accessToken.substring(accessToken.length - 10)}`,
          tokenFormat: accessToken.includes('.') ? 'JWT' : 'Unknown',
          tokenLength: accessToken.length,
          tokenParts: accessToken.split('.').length,
          tokenPayload: JSON.stringify(tokenPayload),
          tokenStart: `${accessToken.substring(0, 10)}...`,
          userId: user.id,
        });

        // Log decoded token parts if it's a JWT
        if (accessToken.includes('.')) {
          const [header, payload] = accessToken.split('.');
          console.log('[SignUpUseCase] Decoded token parts:', {
            header: Buffer.from(header, 'base64').toString(),
            payload: Buffer.from(payload, 'base64').toString(),
          });
        }
      } catch (error) {
        console.error(
          '[SignUpUseCase] Failed to generate tokens:',
          error,
          'Error type:',
          error instanceof Error ? error.constructor.name : 'Unknown',
        );
        // Generate fallback tokens
        session.accessToken = this.createBasicJwtToken(tokenPayload);
        session.refreshToken = crypto.randomBytes(64).toString('hex');
      }
    } else {
      console.warn(
        '[SignUpUseCase] No token service available, using fallback tokens',
      );
      session.accessToken = this.createBasicJwtToken(tokenPayload);
      session.refreshToken = crypto.randomBytes(64).toString('hex');
    }

    // Create proper session if session repository is available
    if (this.sessionRepository) {
      try {
        console.log('[SignUpUseCase] Creating session for user:', user.id);

        const newSession = await this.sessionRepository.createSession({
          accessToken: session.accessToken,
          expiresAt: session.expiresAt,
          isRevoked: false,
          metadata: { type: 'SIGN_UP' },
          refreshToken: session.refreshToken,
          userId: user.id,
        });

        console.log('[SignUpUseCase] Session created successfully:', {
          accessTokenLength: newSession.accessToken?.length,
          hasAccessToken: !!newSession.accessToken,
          hasRefreshToken: !!newSession.refreshToken,
          sessionId: newSession.id,
          tokenStart: `${newSession.accessToken?.substring(0, 10)}...`,
          userId: newSession.userId,
        });

        session = {
          accessToken: newSession.accessToken,
          expiresAt: newSession.expiresAt,
          id: newSession.id,
          refreshToken: newSession.refreshToken || '',
        };
      } catch (error) {
        console.error('[SignUpUseCase] Failed to create session:', error);
        console.error('[SignUpUseCase] Error details:', {
          message: error instanceof Error ? error.message : String(error),
          name: error instanceof Error ? error.constructor.name : 'Unknown',
          stack: error instanceof Error ? error.stack : undefined,
        });
      }
    }

    // Return user data
    return {
      session: {
        accessToken: session.accessToken,
        expiresAt: session.expiresAt,
        id: session.id,
        refreshToken: session.refreshToken,
      },
      user: {
        createdAt: user.createdAt,
        email: user.email,
        id: user.id,
        isActive: true,
        metadata: user.metadata,
        role: user.role === 'STAFF' ? 'USER' : user.role,
        updatedAt: user.updatedAt,
      },
    };
  }
}
