generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
  binaryTargets   = ["native", "darwin-arm64", "darwin"]
}

datasource db {
  provider = "postgresql"
  schemas  = ["public_identity"]
}

model UserPool {
  id                 String             @id @default(uuid())
  name               String
  policies           Json
  emailConfiguration Json
  lambdaTriggers     Json?
  customAttributes   <PERSON><PERSON>[]
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  identityProviders  IdentityProvider[]
  groups             UserGroup[]
  users              User[]

  @@map("user_pools")
  @@schema("public_identity")
}

model User {
  id               String                @id @default(cuid())
  email            String                @unique
  passwordHash     String?
  isActive         Boolean               @default(true)
  emailVerified    <PERSON>olean               @default(false)
  metadata         Json                  @default("{}")
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @updatedAt
  userPoolId       String?
  authChallenge    AuthChallenge[]
  authEvent        AuthEvent[]
  device           Device[]
  riskAssessment   RiskAssessment[]
  securityQuestion SecurityQuestion[]
  sessions         Session[]
  groupMemberships UserGroupMembership[]
  oauthAccounts    userOAuthAccount[]
  organizations    UserOrganization[]
  userPool         UserPool?             @relation(fields: [userPoolId], references: [id])

  @@index([email])
  @@map("users")
  @@schema("public_identity")
}

model Session {
  id           String   @id @default(uuid())
  userId       String
  expiresAt    DateTime
  isRevoked    Boolean  @default(false)
  metadata     Json?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  accessToken  String   @unique
  refreshToken String?  @unique(map: "sessions_refresh_token_key")
  user         User     @relation(fields: [userId], references: [id])

  @@index([accessToken])
  @@index([userId])
  @@index([refreshToken], map: "sessions_refreshtoken_idx")
  @@map("sessions")
  @@schema("public_identity")
}

model UserGroup {
  id          String                @id @default(uuid())
  name        String
  description String?
  precedence  Int
  roleArn     String?
  userPoolId  String
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @updatedAt
  members     UserGroupMembership[]
  userPool    UserPool              @relation(fields: [userPoolId], references: [id])

  @@map("user_groups")
  @@schema("public_identity")
}

model UserGroupMembership {
  id        String    @id @default(uuid())
  userId    String
  groupId   String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  group     UserGroup @relation(fields: [groupId], references: [id])
  user      User      @relation(fields: [userId], references: [id])

  @@unique([userId, groupId])
  @@map("user_group_memberships")
  @@schema("public_identity")
}

model IdentityProvider {
  id               String   @id @default(uuid())
  type             String
  name             String
  userPoolId       String
  providerDetails  Json
  attributeMapping Json
  enabled          Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  userPool         UserPool @relation(fields: [userPoolId], references: [id])

  @@map("identity_providers")
  @@schema("public_identity")
}

model Device {
  id                    String      @id @default(uuid())
  userId                String
  key                   String      @unique
  lastAuthenticatedDate DateTime
  attributes            Json
  status                String
  createdAt             DateTime    @default(now())
  updatedAt             DateTime    @updatedAt
  authEvents            AuthEvent[]
  user                  User        @relation(fields: [userId], references: [id])

  @@map("devices")
  @@schema("public_identity")
}

model SecurityQuestion {
  id        String   @id @default(uuid())
  userId    String
  question  String
  answer    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])

  @@map("security_questions")
  @@schema("public_identity")
}

model AuthEvent {
  id        String   @id @default(uuid())
  userId    String
  deviceId  String?
  type      String
  ipAddress String
  userAgent String
  location  Json?
  metadata  Json
  createdAt DateTime @default(now())
  device    Device?  @relation(fields: [deviceId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@map("auth_events")
  @@schema("public_identity")
}

model RiskAssessment {
  id                String   @id @default(uuid())
  userId            String
  ipAddress         String
  deviceId          String?
  location          Json?
  userAgent         String
  riskLevel         String
  riskScore         Float
  riskFactors       String[]
  recommendedAction String
  contextData       Json
  createdAt         DateTime @default(now())
  user              User     @relation(fields: [userId], references: [id])

  @@map("risk_assessments")
  @@schema("public_identity")
}

model AuthChallenge {
  id         String   @id @default(uuid())
  userId     String
  code       String
  type       String
  expiryDate DateTime
  metadata   Json
  createdAt  DateTime @default(now())
  user       User     @relation(fields: [userId], references: [id])

  @@map("auth_challenges")
  @@schema("public_identity")
}

model oAuthProvider {
  id               String             @id
  name             String
  clientId         String
  clientSecret     String
  authorizationUrl String
  tokenUrl         String
  userInfoUrl      String
  scope            String[]
  enabled          Boolean            @default(true)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  states           oAuthState[]
  userAccounts     userOAuthAccount[]

  @@map("oauth_providers")
  @@schema("public_identity")
}

model oAuthState {
  id         String        @id @default(cuid())
  state      String        @unique
  providerId String
  expiresAt  DateTime
  createdAt  DateTime      @default(now())
  provider   oAuthProvider @relation(fields: [providerId], references: [id])

  @@map("oauth_states")
  @@schema("public_identity")
}

model userOAuthAccount {
  userId         String
  providerId     String
  providerUserId String
  email          String
  name           String?
  picture        String?
  accessToken    String
  refreshToken   String?
  tokenExpiresAt DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  provider       oAuthProvider @relation(fields: [providerId], references: [id])
  user           User          @relation(fields: [userId], references: [id])

  @@id([userId, providerId])
  @@index([providerUserId])
  @@map("user_oauth_accounts")
  @@schema("public_identity")
}

model Organization {
  id          String             @id @default(uuid())
  name        String
  slug        String             @unique
  description String?
  logo        String?
  website     String?
  isActive    Boolean            @default(true)
  metadata    Json               @default("{}")
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  users       UserOrganization[]

  @@map("organizations")
  @@schema("public_identity")
}

model UserOrganization {
  id             String       @id @default(uuid())
  userId         String
  organizationId String
  role           String       @default("USER")
  isActive       Boolean      @default(true)
  isDefault      Boolean      @default(false)
  metadata       Json         @default("{}")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id])
  user           User         @relation(fields: [userId], references: [id])

  @@unique([userId, organizationId])
  @@map("user_organizations")
  @@schema("public_identity")
}

model PasswordHistory {
  id        String   @id @default(cuid())
  userId    String
  hash      String
  createdAt DateTime @default(now())

  @@index([userId, createdAt(sort: Desc)])
  @@map("password_history")
  @@schema("public_identity")
}

enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  CANCELLED
  COMPLETED
  RESCHEDULED

  @@schema("public_identity")
}
