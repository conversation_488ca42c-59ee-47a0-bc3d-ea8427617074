{"author": "Beauty CRM Platform Team", "dependencies": {"@beauty-crm/platform-db-client": "^1.0.4", "@beauty-crm/platform-logger": "^1.0.3", "@beauty-crm/product-domain-types": "^1.0.0", "@beauty-crm/product-identity-types": "^1.0.0", "@hono/node-server": "^1.19.7", "@hono/zod-validator": "^0.7.6", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.67.3", "@opentelemetry/exporter-trace-otlp-http": "^0.208.0", "@opentelemetry/instrumentation": "^0.208.0", "@opentelemetry/resources": "^2.2.0", "@opentelemetry/sdk-node": "^0.208.0", "@opentelemetry/sdk-trace-base": "^2.2.0", "@opentelemetry/semantic-conventions": "^1.38.0", "@paralleldrive/cuid2": "^3.0.6", "@prisma/client": "^7.2.0", "@types/fs-extra": "^11.0.4", "@upstash/redis": "^1.36.0", "argon2": "^0.44.0", "bcryptjs": "^3.0.3", "fs-extra": "^11.3.3", "hono": "^4.11.3", "jsonwebtoken": "^9.0.3", "node-fetch": "^3.3.2", "nodemailer": "^7.0.12", "otplib": "^12.0.1", "qrcode": "^1.5.4", "vite": "^6.4.1", "zod": "^4.3.5", "zxcvbn": "^4.4.2"}, "description": "Identity Management Service for Beauty CRM Tech Stack: Identity Management, Auth, MFA, User Management, etc. Tech Stack: Node.js, Bun, Prisma, Hono, Biome, Vitest, QRCode, Nodemailer, Jsonwebtoken, Otplib, Bcryptjs, Zod, Zxcvbn, QRCode, Nodemailer, Jsonwebtoken, Bcryptjs. ", "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^25.0.3", "@types/nodemailer": "^7.0.4", "@types/qrcode": "^1.5.6", "@types/zxcvbn": "^4.4.5", "prisma": "^7.2.0", "rimraf": "^6.1.2", "ts-node": "^10.9.2", "tsx": "^4.21.0", "typescript": "^5.9.3", "vitest": "^4.0.6"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/public-identity-management-backend", "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"api:auth:test": "mdblaster src/tests/api/auth.blast.md", "api:auth:test:smart": "mdblaster src/tests/api/auth.smart.blast.md", "build": "tsc", "dev": "tsx watch src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:prod": "prisma migrate deploy", "prisma:seed": "prisma db seed", "start": "ts-node src/index.ts", "test": "nx vite:test", "test:e2e": "nx vite:test --config vitest.config.e2e.ts", "verify-email": "node --loader ts-node/esm scripts/verify-email.ts"}, "types": "./dist/index.d.ts", "version": "2.0.0"}