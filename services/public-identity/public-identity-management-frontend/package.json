{"author": "Beauty CRM Platform Team", "dependencies": {"@beauty-crm/platform-introvertic-ui": "^1.0.0", "@tanstack/react-query": "^5.90.16", "axios": "^1.13.2", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "react": "^19.2.3", "react-dom": "^19.2.3", "react-error-boundary": "^6.0.2", "react-icons": "^5.5.0", "react-router-dom": "^7.12.0", "web-vitals": "^5.1.0", "zod": "^4.3.5"}, "description": "Public Identity Management solution frontend", "devDependencies": {"@playwright/test": "^1.57.0", "@testing-library/jest-dom": "^6.9.1", "@testing-library/react": "^16.3.0", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@vitejs/plugin-react": "^5.1.2", "jsdom": "^27.4.0", "typescript": "^5.9.3", "vite": "^7.3.1", "vite-tsconfig-paths": "^5.1.4", "vitest": "^4.0.6"}, "directories": {"lib": "lib", "test": "test"}, "keywords": [], "license": "ISC", "main": "index.js", "module": "index.ts", "name": "@beauty-crm/public-identity-management-frontend", "private": true, "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"build": "tsc && nx vite:build", "dev": "vite", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "nx vite:preview", "test": "nx vite:test run", "test:watch": "nx vite:test", "ui:check": "node ./scripts/ui-check.mjs", "ui:simple": "node ./scripts/simple-ui-check.js"}, "type": "module", "version": "1.0.0"}