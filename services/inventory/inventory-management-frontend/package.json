{"dependencies": {"@mantine/core": "^7.17.8", "@mantine/dates": "^7.17.8", "@mantine/form": "^7.17.8", "@mantine/hooks": "^7.17.8", "@mantine/notifications": "^7.17.8", "@tabler/icons-react": "^3.36.1", "@tanstack/react-query": "^5.90.16", "axios": "^1.13.2", "date-fns": "^4.1.0", "react": "^19.2.3", "react-dom": "^19.2.3", "react-router-dom": "^7.12.0", "zod": "^4.3.5"}, "description": "Inventory management microfrontend for Beauty CRM", "devDependencies": {"@types/node": "^25.0.3", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@vitejs/plugin-react": "^5.1.2", "postcss": "^8.5.6", "postcss-preset-mantine": "^1.18.0", "typescript": "^5.9.3", "vite": "^7.3.1", "vitest": "^4.0.6"}, "name": "@beauty-crm/inventory-management-frontend", "private": true, "scripts": {"build": "tsc && vite build --config vite.federation.config.js", "dev": "vite --config vite.federation.config.js", "dev:shell": "platform-shell-lifecycle start", "dev:vite": "vite", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "1.0.0"}