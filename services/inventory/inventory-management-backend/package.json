{"dependencies": {"@beauty-crm/platform-environment-names": "^1.0.0", "@beauty-crm/platform-identity-client": "^1.0.0", "@beauty-crm/platform-logger": "^1.0.3", "@beauty-crm/platform-utilities": "^1.0.0", "@hono/node-server": "^1.19.7", "@prisma/client": "^7.2.0", "date-fns": "^4.1.0", "hono": "^4.11.3", "ioredis": "^5.9.0", "zod": "^4.3.5"}, "description": "API service managing inventory operations, stock levels, and product catalog for Beauty CRM", "devDependencies": {"@types/node": "^25.0.3", "prisma": "^7.2.0", "rimraf": "^6.1.2", "tsx": "^4.21.0", "typescript": "^5.9.3", "vite": "^7.3.1", "vitest": "^4.0.6"}, "name": "@beauty-crm/inventory-management-backend", "private": true, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsx watch src/server.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "start": "computing-lifecycle start", "start:dev": "computing-lifecycle start", "start:prod": "computing-lifecycle start --app-env prod", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "1.0.0"}