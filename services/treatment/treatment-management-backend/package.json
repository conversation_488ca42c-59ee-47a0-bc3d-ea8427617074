{"dependencies": {"@beauty-crm/platform-db-client": "^1.0.4", "@prisma/adapter-pg": "^7.2.0", "@prisma/client": "^7.2.0", "hono": "^4.11.3", "nats": "^2.29.3", "pg": "^8.13.1", "prisma": "^7.2.0", "zod": "^4.3.5"}, "description": "Treatment catalog management service", "devDependencies": {"@types/bun": "latest", "@types/pg": "^8.16.0"}, "main": "src/index.ts", "module": "index.ts", "name": "@beauty-crm/treatment-management-backend", "peerDependencies": {"typescript": "^5.9.3"}, "private": true, "scripts": {"build": "bun build src/index.ts --outdir dist --target bun", "db:generate": "bunx prisma generate", "db:migrate": "bunx prisma migrate dev", "db:push": "bunx prisma db push", "db:studio": "bunx prisma studio", "dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts"}, "type": "module", "version": "1.0.0"}