import type { Context } from 'hono';
import { z } from 'zod';
import type { TreatmentService } from '../application/services/TreatmentService';

// Validation schemas
const createTreatmentSchema = z.object({
  basePrice: z.number().min(0, 'Price must be non-negative'),
  description: z.string().optional(),
  duration: z.number().min(1, 'Duration must be at least 1 minute'),
  name: z.string().min(1, 'Name is required'),
  salonId: z.string().min(1, 'Salon ID is required'),
});

const updateTreatmentSchema = createTreatmentSchema
  .partial()
  .omit({ salonId: true });

const searchFiltersSchema = z.object({
  isActive: z.coerce.boolean().optional(),
  maxDuration: z.coerce.number().optional(),
  maxPrice: z.coerce.number().optional(),
  minDuration: z.coerce.number().optional(),
  minPrice: z.coerce.number().optional(),
  query: z.string().optional(),
});

const bulkToggleActiveSchema = z.object({
  isActive: z.boolean(),
  treatmentIds: z
    .array(z.string())
    .min(1, 'At least one treatment ID is required'),
});

export class TreatmentController {
  constructor(private treatmentService: TreatmentService) {}

  // GET /api/treatments?salonId=salon1
  getAll = async (c: Context) => {
    try {
      const { salonId } = c.req.query();

      if (!salonId) {
        return c.json({ error: 'Salon ID is required', success: false }, 400);
      }

      const treatments = await this.treatmentService.getAllTreatments(salonId);

      return c.json({
        count: treatments.length,
        data: treatments.map((t) => t.toJSON()),
        success: true,
      });
    } catch (error) {
      console.error('Error getting treatments:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // GET /api/treatments/active?salonId=salon1
  getActive = async (c: Context) => {
    try {
      const { salonId } = c.req.query();

      if (!salonId) {
        return c.json({ error: 'Salon ID is required', success: false }, 400);
      }

      const treatments =
        await this.treatmentService.getActiveTreatments(salonId);

      return c.json({
        count: treatments.length,
        data: treatments.map((t) => t.toJSON()),
        success: true,
      });
    } catch (error) {
      console.error('Error getting active treatments:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // GET /api/treatments/:id
  getById = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json(
          { error: 'Treatment ID is required', success: false },
          400,
        );
      }

      const treatment = await this.treatmentService.getTreatmentById(id);

      if (!treatment) {
        return c.json({ error: 'Treatment not found', success: false }, 404);
      }

      return c.json({ data: treatment.toJSON(), success: true });
    } catch (error) {
      console.error('Error getting treatment by ID:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // GET /api/treatments/search?salonId=salon1&query=facial
  search = async (c: Context) => {
    try {
      const { salonId, ...filters } = c.req.query();

      if (!salonId) {
        return c.json({ error: 'Salon ID is required', success: false }, 400);
      }

      const validationResult = searchFiltersSchema.safeParse(filters);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid search filters',
            success: false,
          },
          400,
        );
      }

      const treatments = await this.treatmentService.searchTreatments(
        salonId,
        validationResult.data,
      );

      return c.json({
        count: treatments.length,
        data: treatments.map((t) => t.toJSON()),
        filters: validationResult.data,
        success: true,
      });
    } catch (error) {
      console.error('Error searching treatments:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // POST /api/treatments
  create = async (c: Context) => {
    try {
      const body = await c.req.json();

      const validationResult = createTreatmentSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid treatment data',
            success: false,
          },
          400,
        );
      }

      const treatment = await this.treatmentService.createTreatment(
        validationResult.data,
      );

      return c.json(
        {
          data: treatment.toJSON(),
          message: 'Treatment created successfully',
          success: true,
        },
        201,
      );
    } catch (error) {
      console.error('Error creating treatment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // PUT /api/treatments/:id
  update = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json(
          { error: 'Treatment ID is required', success: false },
          400,
        );
      }

      const body = await c.req.json();

      const validationResult = updateTreatmentSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid treatment data',
            success: false,
          },
          400,
        );
      }

      const treatment = await this.treatmentService.updateTreatment(
        id,
        validationResult.data,
      );

      return c.json({
        data: treatment.toJSON(),
        message: 'Treatment updated successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error updating treatment:', error);

      if (error instanceof Error && error.message === 'Treatment not found') {
        return c.json({ error: error.message, success: false }, 404);
      }

      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // PATCH /api/treatments/:id/activate
  activate = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json(
          { error: 'Treatment ID is required', success: false },
          400,
        );
      }

      const treatment = await this.treatmentService.activateTreatment(id);

      return c.json({
        data: treatment.toJSON(),
        message: 'Treatment activated successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error activating treatment:', error);

      if (error instanceof Error && error.message === 'Treatment not found') {
        return c.json({ error: error.message, success: false }, 404);
      }

      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // PATCH /api/treatments/:id/deactivate
  deactivate = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json(
          { error: 'Treatment ID is required', success: false },
          400,
        );
      }

      const treatment = await this.treatmentService.deactivateTreatment(id);

      return c.json({
        data: treatment.toJSON(),
        message: 'Treatment deactivated successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error deactivating treatment:', error);

      if (error instanceof Error && error.message === 'Treatment not found') {
        return c.json({ error: error.message, success: false }, 404);
      }

      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // DELETE /api/treatments/:id
  delete = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json(
          { error: 'Treatment ID is required', success: false },
          400,
        );
      }

      await this.treatmentService.deleteTreatment(id);

      return c.json({
        message: 'Treatment deleted successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error deleting treatment:', error);

      if (error instanceof Error && error.message === 'Treatment not found') {
        return c.json({ error: error.message, success: false }, 404);
      }

      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // POST /api/treatments/bulk/toggle-active
  bulkToggleActive = async (c: Context) => {
    try {
      const { salonId } = c.req.query();
      const body = await c.req.json();

      if (!salonId) {
        return c.json({ error: 'Salon ID is required', success: false }, 400);
      }

      const validationResult = bulkToggleActiveSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid bulk toggle data',
            success: false,
          },
          400,
        );
      }

      const updatedCount = await this.treatmentService.bulkToggleActive(
        salonId,
        validationResult.data.treatmentIds,
        validationResult.data.isActive,
      );

      return c.json({
        data: { updatedCount },
        message: `${validationResult.data.isActive ? 'Activated' : 'Deactivated'} ${updatedCount} treatments`,
        success: true,
      });
    } catch (error) {
      console.error('Error bulk toggling active status:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };
}
