// Treatment Domain Schema
// Comprehensive treatment catalog management

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
}

// Core Treatment Model
model Treatment {
  id          String   @id @default(cuid())
  salonId     String // Multi-tenant support
  name        String
  description String?
  duration    Int // Duration in minutes
  basePrice   Decimal // Base price before modifiers
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Rich content
  media TreatmentMedia[]

  // Categorization
  categoryId String?
  category   TreatmentCategory? @relation(fields: [categoryId], references: [id])
  tags       TreatmentTag[]

  // Complex pricing
  pricing TreatmentPricing[]

  // Business rules
  availability TreatmentAvailability[]
  restrictions TreatmentRestriction[]
  addOns       TreatmentAddOn[]

  // Prerequisites and relationships
  prerequisites TreatmentPrerequisite[] @relation("TreatmentPrerequisites")
  dependents    TreatmentPrerequisite[] @relation("PrerequisiteTreatments")

  // Analytics
  analytics TreatmentAnalytics?

  @@index([salonId])
  @@index([categoryId])
  @@index([isActive])
}

// Treatment Categories (hierarchical)
model TreatmentCategory {
  id          String              @id @default(cuid())
  name        String
  description String?
  parentId    String?
  parent      TreatmentCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    TreatmentCategory[] @relation("CategoryHierarchy")
  icon        String? // Icon identifier
  color       String? // Brand color
  sortOrder   Int                 @default(0)
  isActive    Boolean             @default(true)
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  treatments Treatment[]

  @@unique([name, parentId])
}

// Treatment Media (images, videos, icons)
model TreatmentMedia {
  id          String    @id @default(cuid())
  treatmentId String
  treatment   Treatment @relation(fields: [treatmentId], references: [id], onDelete: Cascade)

  type      MediaType
  url       String
  altText   String?
  caption   String?
  sortOrder Int       @default(0)
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())

  @@index([treatmentId])
}

enum MediaType {
  IMAGE
  VIDEO
  ICON
  THUMBNAIL
}

// Complex Pricing Rules
model TreatmentPricing {
  id          String    @id @default(cuid())
  treatmentId String
  treatment   Treatment @relation(fields: [treatmentId], references: [id], onDelete: Cascade)

  name         String // e.g., "Weekend Premium", "Member Discount"
  type         PricingType
  value        Decimal // Price or percentage
  isPercentage Boolean     @default(false)

  // Conditions
  validFrom DateTime?
  validTo   DateTime?
  dayOfWeek String? // JSON array of days
  timeFrom  String? // HH:MM format
  timeTo    String? // HH:MM format

  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([treatmentId])
}

enum PricingType {
  BASE_PRICE
  SEASONAL_PREMIUM
  WEEKEND_PREMIUM
  MEMBER_DISCOUNT
  SPECIALIST_PREMIUM
  LOCATION_MODIFIER
}

// Treatment Tags (flexible labeling)
model TreatmentTag {
  id          String   @id @default(cuid())
  name        String   @unique
  color       String?
  description String?
  createdAt   DateTime @default(now())

  treatments Treatment[]
}

// Treatment Availability Rules
model TreatmentAvailability {
  id          String    @id @default(cuid())
  treatmentId String
  treatment   Treatment @relation(fields: [treatmentId], references: [id], onDelete: Cascade)

  dayOfWeek String // JSON array of available days
  timeFrom  String // HH:MM format
  timeTo    String // HH:MM format

  validFrom DateTime?
  validTo   DateTime?

  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  @@index([treatmentId])
}

// Treatment Restrictions
model TreatmentRestriction {
  id          String    @id @default(cuid())
  treatmentId String
  treatment   Treatment @relation(fields: [treatmentId], references: [id], onDelete: Cascade)

  type        RestrictionType
  description String
  value       String? // Additional data (age limit, etc.)

  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  @@index([treatmentId])
}

enum RestrictionType {
  AGE_MINIMUM
  AGE_MAXIMUM
  GENDER_SPECIFIC
  HEALTH_CONDITION
  PREGNANCY_RESTRICTION
  SKIN_TYPE
  CUSTOM
}

// Treatment Add-ons
model TreatmentAddOn {
  id          String    @id @default(cuid())
  treatmentId String
  treatment   Treatment @relation(fields: [treatmentId], references: [id], onDelete: Cascade)

  name        String
  description String?
  price       Decimal
  duration    Int // Additional duration in minutes
  isOptional  Boolean @default(true)

  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([treatmentId])
}

// Treatment Prerequisites (treatment dependencies)
model TreatmentPrerequisite {
  id                      String @id @default(cuid())
  treatmentId             String
  prerequisiteTreatmentId String

  treatment             Treatment @relation("TreatmentPrerequisites", fields: [treatmentId], references: [id], onDelete: Cascade)
  prerequisiteTreatment Treatment @relation("PrerequisiteTreatments", fields: [prerequisiteTreatmentId], references: [id], onDelete: Cascade)

  description String?
  isRequired  Boolean @default(true)
  daysBetween Int? // Minimum days between treatments

  createdAt DateTime @default(now())

  @@unique([treatmentId, prerequisiteTreatmentId])
}

// Treatment Analytics
model TreatmentAnalytics {
  id          String    @id @default(cuid())
  treatmentId String    @unique
  treatment   Treatment @relation(fields: [treatmentId], references: [id], onDelete: Cascade)

  totalBookings   Int     @default(0)
  totalRevenue    Decimal @default(0)
  averageRating   Decimal @default(0)
  popularityScore Decimal @default(0)

  lastBookedAt DateTime?
  updatedAt    DateTime  @updatedAt
}
