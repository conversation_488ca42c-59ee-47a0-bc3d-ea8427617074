# Database Migration Sidecar for Salon Service
FROM oven/bun:1.3.5-alpine AS base

# Install Node.js 22 (required for Prisma v7.2.0) and PostgreSQL client
RUN apk add --no-cache postgresql-client nodejs npm

# Set working directory
WORKDIR /app

# Create minimal package.json for migration (including adapter dependencies for Prisma 7)
RUN echo '{"name":"@beauty-crm/salon-db-migrator","version":"1.0.0","type":"module","dependencies":{"@prisma/client":"^7.2.0","@prisma/adapter-pg":"^7.2.0","pg":"^8.13.1","prisma":"^7.2.0"}}' > package.json

# Install minimal dependencies for migration
RUN bun install --production

# Copy Prisma schema, config, and migrations
COPY services/salon/salon-management-backend/prisma ./prisma/
COPY services/salon/salon-management-backend/prisma.config.ts ./prisma.config.ts

# Migration scripts are generated inline below

# Generate Prisma client
RUN bunx prisma generate

# Add migration script
RUN echo '#!/bin/sh' > /app/migrate.sh && \
    echo 'echo "🗄️ Starting database migration for salon service..."' >> /app/migrate.sh && \
    echo 'echo "Database URL: $DATABASE_URL"' >> /app/migrate.sh && \
    echo '' >> /app/migrate.sh && \
    echo '# Wait for database to be ready' >> /app/migrate.sh && \
    echo 'until pg_isready -h beauty_crm_postgres -p 5432 -U beauty_crm; do' >> /app/migrate.sh && \
    echo '  echo "⏳ Waiting for PostgreSQL to be ready..."' >> /app/migrate.sh && \
    echo '  sleep 2' >> /app/migrate.sh && \
    echo 'done' >> /app/migrate.sh && \
    echo '' >> /app/migrate.sh && \
    echo 'echo "✅ PostgreSQL is ready!"' >> /app/migrate.sh && \
    echo '' >> /app/migrate.sh && \
    echo '# Run Prisma migrations' >> /app/migrate.sh && \
    echo 'echo "🔄 Running Prisma migrations..."' >> /app/migrate.sh && \
    echo 'bunx prisma migrate deploy' >> /app/migrate.sh && \
    echo '' >> /app/migrate.sh && \
    echo '# Generate Prisma client (in case of schema changes)' >> /app/migrate.sh && \
    echo 'echo "🔧 Generating Prisma client..."' >> /app/migrate.sh && \
    echo 'bunx prisma generate' >> /app/migrate.sh && \
    echo '' >> /app/migrate.sh && \
    echo '# Optional: Run seeds if they exist' >> /app/migrate.sh && \
    echo 'if [ -f "prisma/seed.ts" ]; then' >> /app/migrate.sh && \
    echo '  echo "🌱 Running database seeds..."' >> /app/migrate.sh && \
    echo '  bun run prisma/seed.ts' >> /app/migrate.sh && \
    echo 'fi' >> /app/migrate.sh && \
    echo '' >> /app/migrate.sh && \
    echo 'echo "🎉 Database migration completed successfully!"' >> /app/migrate.sh && \
    chmod +x /app/migrate.sh

# Default command - run migration script directly
CMD ["./migrate.sh"]
