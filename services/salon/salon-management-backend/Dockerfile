# Optimized Multi-Stage Dockerfile for Salon Management Backend
# Uses Verdaccio for @beauty-crm packages

# Stage 1: Dependencies stage
FROM oven/bun:1.2.15-alpine AS deps
WORKDIR /app/services/salon/salon-management-backend

# Copy only service files (not root package.json to avoid workspace mode)
COPY services/salon/salon-management-backend/package.json ./package.json

# Replace workspace:* with version numbers for Verdaccio resolution
RUN sed -i 's/workspace:\*/^1.0.0/g' ./package.json && cat ./package.json | grep "@beauty-crm"

# Remove bun.lock to force fresh resolution from Verdaccio
RUN rm -f bun.lock

# Configure npm/bun to use <PERSON><PERSON><PERSON>cio for @beauty-crm packages
# Use host.docker.internal to reach Verdaccio running on host
RUN echo '@beauty-crm:registry=http://host.docker.internal:4873/' > ./.npmrc && \
    echo 'registry=https://registry.npmjs.org/' >> ./.npmrc

# Create bunfig.toml for Bun with proper newlines
RUN cat > ./bunfig.toml << 'EOF'
[install]
registry = "http://host.docker.internal:4873"

[install.scopes]
"@beauty-crm" = { url = "http://host.docker.internal:4873/" }
EOF

# Install production dependencies from Verdaccio
# Use --ignore-scripts to skip any postinstall scripts that might reference workspace deps
RUN bun install --production --ignore-scripts

# Stage 2: Build stage
FROM oven/bun:1.2.15-alpine AS build
WORKDIR /app/services/salon/salon-management-backend

# Copy only service files (not root package.json to avoid workspace mode)
COPY services/salon/salon-management-backend/package.json ./package.json
COPY services/salon/salon-management-backend/src ./src
COPY services/salon/salon-management-backend/tsconfig.json ./tsconfig.json
COPY services/salon/salon-management-backend/prisma ./prisma

# Replace workspace:* with version numbers for Verdaccio resolution
RUN sed -i 's/workspace:\*/^1.0.0/g' ./package.json

# Remove bun.lock to force fresh resolution from Verdaccio
RUN rm -f bun.lock

# Configure npm/bun to use Verdaccio for @beauty-crm packages
# Use host.docker.internal to reach Verdaccio running on host
RUN echo '@beauty-crm:registry=http://host.docker.internal:4873/' > ./.npmrc && \
    echo 'registry=https://registry.npmjs.org/' >> ./.npmrc

# Create bunfig.toml for Bun with proper newlines
RUN cat > ./bunfig.toml << 'EOF'
[install]
registry = "http://host.docker.internal:4873"

[install.scopes]
"@beauty-crm" = { url = "http://host.docker.internal:4873/" }
EOF

# Install all dependencies (including dev) - NO workspace mode
# Use --ignore-scripts to skip any postinstall scripts that might reference workspace deps
# Force rebuild by adding timestamp
RUN bun install --ignore-scripts

# Generate Prisma client and build
# Prisma generate doesn't need database connection - unset DATABASE_URL if present
RUN if [ -f "prisma/schema.prisma" ]; then \
      unset DATABASE_URL && \
      bunx prisma generate --schema=prisma/schema.prisma; \
    fi
RUN bun run build

# Stage 3: Production stage
FROM oven/bun:1.2.15-alpine AS production

# Install dumb-init for proper signal handling and security updates
RUN apk add --no-cache dumb-init curl

# Set production environment
ENV NODE_ENV=production
ENV HOST=0.0.0.0
ENV PORT=4000

WORKDIR /app

# Create non-root user for security (Alpine Linux)
RUN addgroup -g 1001 -S nodejs && \
    adduser -S -u 1001 -G nodejs nodejs

# Copy production dependencies from deps stage
COPY --from=deps --chown=nodejs:nodejs /app/services/salon/salon-management-backend/node_modules ./node_modules

# Copy built application and generated Prisma client from build stage
COPY --from=build --chown=nodejs:nodejs /app/services/salon/salon-management-backend/dist ./dist
COPY --from=build --chown=nodejs:nodejs /app/services/salon/salon-management-backend/prisma ./prisma
COPY --from=build --chown=nodejs:nodejs /app/services/salon/salon-management-backend/node_modules/.prisma ./node_modules/.prisma

# Switch to non-root user
USER nodejs

EXPOSE 4000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:4000/health || exit 1

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["bun", "run", "dist/index.js"]
