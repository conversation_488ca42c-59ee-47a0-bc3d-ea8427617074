{"dependencies": {"@beauty-crm/platform-db-client": "^1.0.4", "@beauty-crm/platform-eventing": "^1.0.0", "@beauty-crm/product-queue-names": "^1.0.0", "@beauty-crm/platform-computing-lifecycle": "^1.0.0", "@beauty-crm/platform-utilities": "^1.0.0", "@hono/node-server": "^1.19.7", "@paralleldrive/cuid2": "^3.0.6", "@prisma/adapter-pg": "^7.2.0", "@prisma/client": "^7.2.0", "axios": "^1.13.2", "hono": "^4.11.3", "nats": "^2.29.3", "pg": "^8.13.1", "uuid": "^13.0.0", "zod": "^4.3.5"}, "devDependencies": {"@cucumber/cucumber": "^12.5.0", "@types/bun": "latest", "@types/chai": "^5.2.2", "@types/node": "^25.0.3", "@types/pg": "^8.16.0", "@types/supertest": "^6.0.3", "@types/uuid": "^11.0.0", "chai": "^6.2.2", "dotenv": "^17.2.3", "esbuild": "^0.27.2", "prisma": "^7.2.0", "rimraf": "^6.1.2", "slugify": "^1.6.6", "supertest": "^7.2.2", "ts-node": "^10.9.2", "tsx": "^4.21.0", "typescript": "^5.8.3", "vitest": "^4.0.6"}, "name": "@beauty-crm/salon-management-backend", "peerDependencies": {"typescript": "^5"}, "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "deploy:dev": "npm run deploy:dev -w @beauty-crm/platform-computing-lifecycle", "deploy:dev:iaac": "cdktf deploy --app \"npx ts-node ../../../shared-platform-engineering/shared-computing-lifecycle/src/platforms/vercel/main.ts\"", "deploy:prod": "npm run deploy:prod -w @beauty-crm/platform-computing-lifecycle", "deploy:stage": "npm run deploy:stage -w @beauty-crm/platform-computing-lifecycle", "dev": "bun src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prisma:generate": "bunx prisma generate", "prisma:migrate": "bunx prisma migrate dev", "prisma:seed": "DATABASE_URL=postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_salon tsx prisma/seed.ts", "prisma:validate": "bunx prisma validate", "start": "bun src/index.ts", "start:dev": "bun src/index.ts", "start:prod": "bun src/index.ts", "start:stage": "bun src/index.ts", "test": "echo 'Not implemented'", "test:coverage": "vitest run --coverage", "test:cucumber": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js", "test:cucumber:advanced-database-manager": "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/beauty_crm?schema=public NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/advanced-database-manager/advanced-database-manager.feature --import src/tests/features/advanced-database-manager/advanced-database-manager.steps.ts", "test:cucumber:database-manager": "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/beauty_crm?schema=public NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/database-manager/database-manager.feature --import src/tests/features/database-manager/database-manager.steps.ts", "test:cucumber:list-salons": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/list-salons/list-salons.feature --import src/tests/features/list-salons/list-salons.steps.ts", "test:watch": "vitest", "vercel:deploy": "npx vercel --prod --confirm --name salon-management-backend --json"}, "type": "module", "version": "1.0.0"}