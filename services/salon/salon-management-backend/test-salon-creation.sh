#!/bin/bash

# Test script to verify salon creation in database
# This script creates a salon via API and verifies it exists in the database

set -e

echo "🧪 Testing Salon Creation..."
echo ""

# Configuration
API_URL="http://localhost:4000"
DB_CONTAINER="beauty_crm_database"
DB_NAME="beauty_crm_salon"
DB_USER="beauty_crm"

# Generate unique salon data
TIMESTAMP=$(date +%s)
SALON_NAME="Test Salon ${TIMESTAMP}"
SALON_EMAIL="test-${TIMESTAMP}@example.com"
SALON_PHONE="+1234567890"
SALON_LOCATION="Test Location ${TIMESTAMP}"

echo "📝 Creating salon via API..."
echo "   Name: ${SALON_NAME}"
echo "   Email: ${SALON_EMAIL}"
echo ""

# Create salon via API
RESPONSE=$(curl -s -X POST "${API_URL}/salons" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"${SALON_NAME}\",
    \"email\": \"${SALON_EMAIL}\",
    \"phone\": \"${SALON_PHONE}\",
    \"locationName\": \"${SALON_LOCATION}\",
    \"timezone\": \"America/New_York\",
    \"businessHoursStart\": \"09:00\",
    \"businessHoursEnd\": \"17:00\"
  }")

echo "📥 API Response:"
echo "${RESPONSE}" | jq .
echo ""

# Extract salon ID from response
SALON_ID=$(echo "${RESPONSE}" | jq -r '.data.id')

if [ -z "${SALON_ID}" ] || [ "${SALON_ID}" = "null" ]; then
  echo "❌ Failed to create salon - no ID returned"
  exit 1
fi

echo "✅ Salon created with ID: ${SALON_ID}"
echo ""

# Wait a moment for database write
sleep 1

echo "🔍 Verifying salon exists in database..."

# Query database to verify salon exists
DB_QUERY="SELECT id, name, email, \"locationName\", timezone, \"businessHoursStart\", \"businessHoursEnd\", \"isActive\" FROM \"Salon\" WHERE id = '${SALON_ID}';"

DB_RESULT=$(docker exec ${DB_CONTAINER} psql -U ${DB_USER} -d ${DB_NAME} -t -c "${DB_QUERY}")

if [ -z "${DB_RESULT}" ]; then
  echo "❌ Salon not found in database!"
  exit 1
fi

echo "📊 Database Record:"
echo "${DB_RESULT}"
echo ""

# Verify specific fields
echo "🔎 Verifying salon data..."

# Get salon via API to compare
GET_RESPONSE=$(curl -s "${API_URL}/salons/${SALON_ID}")
echo "📥 GET Response:"
echo "${GET_RESPONSE}" | jq .
echo ""

# Extract and verify fields
API_NAME=$(echo "${GET_RESPONSE}" | jq -r '.data.name')
API_EMAIL=$(echo "${GET_RESPONSE}" | jq -r '.data.email')
API_ACTIVE=$(echo "${GET_RESPONSE}" | jq -r '.data.isActive')

if [ "${API_NAME}" != "${SALON_NAME}" ]; then
  echo "❌ Name mismatch! Expected: ${SALON_NAME}, Got: ${API_NAME}"
  exit 1
fi

if [ "${API_EMAIL}" != "${SALON_EMAIL}" ]; then
  echo "❌ Email mismatch! Expected: ${SALON_EMAIL}, Got: ${API_EMAIL}"
  exit 1
fi

if [ "${API_ACTIVE}" != "true" ]; then
  echo "❌ Salon should be active by default!"
  exit 1
fi

echo "✅ All fields verified successfully!"
echo ""

# Cleanup - delete the test salon
echo "🧹 Cleaning up test data..."
DELETE_RESPONSE=$(curl -s -X DELETE "${API_URL}/salons/${SALON_ID}")
echo "Delete response: ${DELETE_RESPONSE}" | jq .
echo ""

echo "✅ Test completed successfully!"
echo ""
echo "Summary:"
echo "  ✓ Salon created via API"
echo "  ✓ Salon exists in database"
echo "  ✓ All fields match expected values"
echo "  ✓ Test data cleaned up"

