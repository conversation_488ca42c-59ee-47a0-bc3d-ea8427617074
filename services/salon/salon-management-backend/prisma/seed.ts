import { PrismaClient } from '@prisma/client';
import { PrismaPg } from '@prisma/adapter-pg';
import pg from 'pg';

// Initialize Prisma client with Prisma 7 adapter (same pattern as index.ts)
const connectionString =
  process.env.DATABASE_URL ||
  '********************************************************************/beauty_crm_salon';
const pool = new pg.Pool({ connectionString });
const adapter = new PrismaPg(pool);
const prisma = new PrismaClient({ adapter });

async function main(): Promise<void> {
  try {
    console.log('🌱 Starting salon database seeding...');

    // Use upsert to create or update test salon - this prevents unique constraint violations
    // All salons use the same database with table-level isolation via salonId
    const salon = await prisma.salon.upsert({
      create: {
        businessHoursEnd: '17:00',
        businessHoursStart: '09:00',
        databaseUrl:
          'postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_salon',
        email: '<EMAIL>',
        id: 'salon1',
        isActive: true,
        locationName: 'amsterdam',
        name: 'Beauty Salon Amsterdam',
        phone: '+***********',
        timezone: 'Europe/Amsterdam',
      },
      update: {
        businessHoursEnd: '17:00',
        businessHoursStart: '09:00',
        databaseUrl:
          'postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_salon',
        email: '<EMAIL>',
        isActive: true,
        locationName: 'amsterdam',
        name: 'Beauty Salon Amsterdam',
        phone: '+***********',
        timezone: 'Europe/Amsterdam',
        updatedAt: new Date(),
      },
      where: {
        id: 'salon1',
      },
    });

    console.log(`✅ Salon seeded successfully: ${salon.name} (${salon.id})`);
    console.log('🎉 Seed completed successfully');
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

try {
  await main();
} catch (error) {
  console.error('Failed to seed database:', error);
  process.exit(1);
} finally {
  await prisma.$disconnect();
  process.exit(0);
}
