generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "darwin", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
}

model Salon {
  id                 String   @id @default(cuid())
  name               String
  email              String   @unique
  phone              String?
  locationName       String
  timezone           String
  businessHoursStart String
  businessHoursEnd   String
  isActive           Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  databaseUrl        String
  parentSalonId      String?
  parentSalon        Salon?   @relation("SalonToSalon", fields: [parentSalonId], references: [id])
  childSalons        Salon[]  @relation("SalonToSalon")

  @@index([locationName])
  @@index([parentSalonId])
  @@map("salons")
}
