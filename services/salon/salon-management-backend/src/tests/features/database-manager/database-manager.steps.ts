import { After, Before, Given, Then, When } from '@cucumber/cucumber';
import { PrismaClient } from '@prisma/client';
import { expect } from 'chai';

type Salon = {
  id: string;
  databaseUrl: string;
};

let cleanupDatabases: string[] = [];
let lastWarning = '';
let prismaClient: PrismaClient | undefined;

export function getPrismaClient(): PrismaClient {
  if (!prismaClient) {
    // In Prisma v7, datasource URL is configured in prisma.config.ts
    // Prisma v7 requires at least an empty options object
    prismaClient = new PrismaClient({});
  }
  return prismaClient;
}

export async function disconnectPrisma(): Promise<void> {
  if (prismaClient) {
    await prismaClient.$disconnect();
    prismaClient = undefined;
  }
}

Before(async () => {
  const prisma = getPrismaClient();

  // Clean up any test salons from previous runs
  await prisma.salon.deleteMany({});

  // Clean up any test databases from previous runs
  for (const dbUrl of cleanupDatabases) {
    const dbName = dbUrl.split('/').pop()?.split('?')[0];
    if (dbName) {
      await prisma.$executeRawUnsafe(`DROP DATABASE IF EXISTS "${dbName}"`);
    }
  }
  cleanupDatabases = [];
});

After(async () => {
  const prisma = getPrismaClient();

  // Clean up test salons
  await prisma.salon.deleteMany({});

  // Clean up test databases
  for (const dbUrl of cleanupDatabases) {
    const dbName = dbUrl.split('/').pop()?.split('?')[0];
    if (dbName) {
      await prisma.$executeRawUnsafe(`DROP DATABASE IF EXISTS "${dbName}"`);
    }
  }
  await disconnectPrisma();
});

async function createDatabaseUrl(
  salonId: string,
  parentId?: string,
): Promise<string | undefined> {
  console.log(
    `Getting standard database URL for salon ${salonId}${parentId ? ` with parent ${parentId}` : ''}`,
  );

  // All salons use the same database URL - database-per-service pattern
  const dbUrl =
    'postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_salon';
  console.log('Using standard database URL:', dbUrl);
  return dbUrl;
}

Given(
  'there is a salon with ID {string} with its own database URL',
  async (salonId: string) => {
    const prisma = getPrismaClient();

    const dbUrl = await createDatabaseUrl(salonId);
    if (!dbUrl) throw new Error('Failed to create database URL');

    await prisma.salon.create({
      data: {
        businessHoursEnd: '17:00',
        businessHoursStart: '09:00',
        databaseUrl: dbUrl,
        email: `${salonId}@test.com`,
        id: salonId,
        isActive: true,
        locationName: 'Test Location',
        name: 'Test Salon',
        phone: '+1234567890',
        timezone: 'UTC',
      },
    });
    cleanupDatabases.push(dbUrl);
  },
);

Given('there is no salon with ID {string}', async (_salonId: string) => {
  // No need to do anything, the ID doesn't exist
  return;
});

Given('the salon ID {string} is malformed', async (_salonId: string) => {
  // No need to do anything, we'll just use this ID in the next step
  return;
});

Given(
  'there is a salon with ID {string} that has a database URL',
  async (salonId: string) => {
    const prisma = getPrismaClient();

    const dbUrl = await createDatabaseUrl(salonId);
    if (!dbUrl) throw new Error('Failed to create database URL');
    cleanupDatabases.push(dbUrl);

    await prisma.salon.create({
      data: {
        businessHoursEnd: '17:00',
        businessHoursStart: '09:00',
        databaseUrl: dbUrl,
        email: `${salonId}@test.com`,
        id: salonId,
        isActive: true,
        locationName: 'Test Location',
        name: 'Test Salon',
        phone: '+1234567890',
        timezone: 'UTC',
      },
    });
  },
);

Given(
  'there is a salon with ID {string} that inherits from parent {string}',
  async (salonId: string, parentId: string) => {
    const prisma = getPrismaClient();

    // First create the parent salon with its database
    const parentDbUrl = await createDatabaseUrl(parentId);
    if (!parentDbUrl) throw new Error('Failed to create database URL');
    cleanupDatabases.push(parentDbUrl);

    await prisma.salon.create({
      data: {
        businessHoursEnd: '17:00',
        businessHoursStart: '09:00',
        databaseUrl: parentDbUrl,
        email: `${parentId}@test.com`,
        id: parentId,
        isActive: true,
        locationName: 'Parent Location',
        name: 'Parent Salon',
        phone: '+1234567890',
        timezone: 'UTC',
      },
    });

    // Then create the child salon using parent's database
    await prisma.salon.create({
      data: {
        businessHoursEnd: '17:00',
        businessHoursStart: '09:00',
        databaseUrl: parentDbUrl,
        email: `${salonId}@test.com`,
        id: salonId,
        isActive: true,
        locationName: 'Child Location',
        name: 'Child Salon',
        parentSalonId: parentId,
        phone: '+1234567890',
        timezone: 'UTC',
      },
    });
  },
);

Given(
  'there is a salon with ID {string} that has no database URL',
  async (salonId: string) => {
    const prisma = getPrismaClient();
    await prisma.salon.create({
      data: {
        businessHoursEnd: '17:00',
        businessHoursStart: '09:00',
        databaseUrl: '',
        email: `${salonId}@test.com`,
        id: salonId,
        isActive: true,
        locationName: 'Parent Location',
        name: 'Parent Salon',
        phone: '+1234567890',
        timezone: 'UTC',
      },
    });
  },
);

export function setWarning(warning: string): void {
  lastWarning = warning;
}

When(
  'I create a salon with ID {string} and parent ID {string}',
  async (salonId: string, parentId: string) => {
    const prisma = getPrismaClient();
    const parent = await prisma.salon.findUnique({
      select: { databaseUrl: true, id: true },
      where: { id: parentId },
    });

    if (parent?.databaseUrl) {
      await prisma.salon.create({
        data: {
          businessHoursEnd: '17:00',
          businessHoursStart: '09:00',
          databaseUrl: parent.databaseUrl,
          email: `${salonId}@test.com`,
          id: salonId,
          isActive: true,
          locationName: 'Test Location',
          name: 'Test Salon',
          parentSalonId: parentId,
          phone: '+1234567890',
          timezone: 'UTC',
        },
      });
    } else {
      // Set different warning message based on whether parent exists or not
      if (parent && parent.databaseUrl === '') {
        lastWarning = 'Parent salon found but has no database URL';
      } else {
        lastWarning = `Could not find valid parent salon with ID ${parentId}`;
      }

      const dbUrl = await createDatabaseUrl(salonId, parentId);
      if (!dbUrl) throw new Error('Failed to create database URL');
      cleanupDatabases.push(dbUrl);

      await prisma.salon.create({
        data: {
          businessHoursEnd: '17:00',
          businessHoursStart: '09:00',
          databaseUrl: dbUrl,
          email: `${salonId}@test.com`,
          id: salonId,
          isActive: true,
          locationName: 'Test Location',
          name: 'Test Salon',
          phone: '+1234567890',
          timezone: 'UTC',
        },
      });
    }
  },
);

Then(
  'a new database URL should be created for salon {string}',
  async (salonId: string) => {
    const prisma = getPrismaClient();
    const salon = await prisma.salon.findUnique({
      select: { databaseUrl: true },
      where: { id: salonId },
    });

    expect(salon).to.not.be.null;
    expect(salon?.databaseUrl).to.include(`salon_${salonId}`);
  },
);

Then('the database URL should not be linked to any other salon', async () => {
  const prisma = getPrismaClient();
  const salons = await prisma.salon.findMany({
    select: { databaseUrl: true, id: true },
  });

  const databaseUrls = salons.map((s: Salon) => s.databaseUrl);
  const uniqueUrls = new Set(databaseUrls);

  expect(databaseUrls.length).to.equal(uniqueUrls.size);
});

Then(
  'the salon {string} should use the same database URL as {string}',
  async (childId: string, parentId: string) => {
    const prisma = getPrismaClient();
    const [child, parent] = await Promise.all([
      prisma.salon.findUnique({
        select: { databaseUrl: true },
        where: { id: childId },
      }),
      prisma.salon.findUnique({
        select: { databaseUrl: true },
        where: { id: parentId },
      }),
    ]);

    expect(child).to.not.be.null;
    expect(parent).to.not.be.null;
    expect(child?.databaseUrl).to.equal(parent?.databaseUrl);
  },
);

Then('no new database URL should be created', async () => {
  const prisma = getPrismaClient();
  const salons = await prisma.salon.findMany({
    select: { databaseUrl: true },
  });

  const uniqueUrls = new Set(
    salons.map((s: { databaseUrl: string }) => s.databaseUrl),
  );
  expect(uniqueUrls.size).to.equal(1);
});

Then('the system should log a warning about invalid parent ID', () => {
  expect(lastWarning).to.be.a('string').and.not.empty;
  expect(lastWarning).to.include('parent');
});

When(
  'I update salon {string} to have parent ID {string}',
  async (salonId: string, parentId: string) => {
    const prisma = getPrismaClient();
    const parent = await prisma.salon.findUnique({
      select: { databaseUrl: true },
      where: { id: parentId },
    });

    if (!parent?.databaseUrl) {
      throw new Error('Parent salon not found or has no database URL');
    }

    await prisma.salon.update({
      data: {
        databaseUrl: parent.databaseUrl,
        parentSalonId: parentId,
      },
      where: { id: salonId },
    });
  },
);

Then(
  'salon {string} should now use the database URL of {string}',
  async (childId: string, parentId: string) => {
    const prisma = getPrismaClient();
    const [child, parent] = await Promise.all([
      prisma.salon.findUnique({
        select: { databaseUrl: true },
        where: { id: childId },
      }),
      prisma.salon.findUnique({
        select: { databaseUrl: true },
        where: { id: parentId },
      }),
    ]);

    expect(child).to.not.be.null;
    expect(parent).to.not.be.null;
    expect(child?.databaseUrl).to.equal(parent?.databaseUrl);
  },
);

Then(
  'the old database for {string} should be marked for cleanup',
  async (salonId: string) => {
    const oldDbUrl = `postgresql://postgres:postgres@localhost:5432/salon_${salonId}`;
    expect(cleanupDatabases).to.include(oldDbUrl);
  },
);

When('I update salon {string} to have no parent', async (salonId: string) => {
  const prisma = getPrismaClient();
  const dbUrl = await createDatabaseUrl(salonId);
  if (!dbUrl) throw new Error('Failed to create database URL');
  cleanupDatabases.push(dbUrl);

  await prisma.salon.update({
    data: {
      databaseUrl: dbUrl,
      parentSalonId: null,
    },
    where: { id: salonId },
  });
});

Then(
  "the salon should no longer be linked to the parent's database",
  async () => {
    const prisma = getPrismaClient();
    const salons = await prisma.salon.findMany({
      select: { databaseUrl: true, id: true },
    });

    const databaseUrls = salons.map(
      (s: { databaseUrl: string }) => s.databaseUrl,
    );
    const uniqueUrls = new Set(databaseUrls);

    expect(databaseUrls.length).to.equal(uniqueUrls.size);
  },
);

Then('the system should log a warning about parent having no database', () => {
  expect(lastWarning).to.be.a('string').and.not.empty;
  expect(lastWarning).to.include('database');
});
