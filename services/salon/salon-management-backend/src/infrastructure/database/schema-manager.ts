import { PrismaClient } from '@prisma/client';
import { PrismaPg } from '@prisma/adapter-pg';
import pg from 'pg';

/**
 * SchemaManager - Refactored for Database-per-Service Pattern
 *
 * This class no longer creates separate schemas per salon.
 * All salons use the same database schema with table-level isolation via SalonId.
 */
export class SchemaManager {
  private prisma: PrismaClient;

  constructor() {
    // Prisma v7 requires adapter for PostgreSQL
    const connectionString =
      process.env.DATABASE_URL ||
      '********************************************************************/beauty_crm_salon';
    const pool = new pg.Pool({ connectionString });
    const adapter = new PrismaPg(pool);
    this.prisma = new PrismaClient({ adapter });
  }

  /**
   * Initialize the shared schema - no longer creates salon-specific schemas
   */
  async init() {
    // No longer needed - using standard Prisma migrations for shared schema
    console.log('SchemaManager initialized for shared database schema');
  }

  /**
   * Create salon record in shared database - no longer creates separate schemas
   * Returns a standard schema name for all salons
   */
  async createSchema(
    salonId: string,
    locationName: string,
    parentId?: string,
  ): Promise<string> {
    // All salons use the same schema name - no salon-specific schemas
    const standardSchemaName = 'public';

    // If this is a child salon, just return the standard schema name
    if (parentId) {
      console.log(
        `Creating child salon ${salonId} under parent ${parentId} in shared schema`,
      );
      return standardSchemaName;
    }

    console.log(`Creating salon ${salonId} (${locationName}) in shared schema`);
    return standardSchemaName;
  }

  /**
   * No longer drops schemas - all salons use shared schema
   */
  async dropSchema(_schemaName: string): Promise<void> {
    console.log('Schema drop skipped - using shared database schema');
  }

  /**
   * Returns standard schema name for all salons
   */
  async getSchemaForSalon(_salonId: string): Promise<string | null> {
    return 'public';
  }

  /**
   * Returns standard schema name - no longer updates schemas per salon
   */
  async updateSalonSchema(
    _salonId: string,
    _parentId?: string,
  ): Promise<string> {
    return 'public';
  }
}
