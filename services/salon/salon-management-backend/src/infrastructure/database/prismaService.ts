import { PrismaClient } from '@prisma/client';
import { PrismaPg } from '@prisma/adapter-pg';
import pg from 'pg';

export class PrismaService {
  private static instance: PrismaService;
  private prisma: PrismaClient;

  private constructor() {
    // Prisma v7 requires adapter for PostgreSQL
    const connectionString =
      process.env.DATABASE_URL ||
      '********************************************************************/beauty_crm_salon';
    const pool = new pg.Pool({ connectionString });
    const adapter = new PrismaPg(pool);

    this.prisma = new PrismaClient({
      adapter,
      errorFormat: 'pretty',
      log:
        process.env.NODE_ENV === 'development'
          ? ['query', 'info', 'warn', 'error']
          : ['error'],
    });
  }

  public static getInstance(): PrismaService {
    if (!PrismaService.instance) {
      PrismaService.instance = new PrismaService();
    }
    return PrismaService.instance;
  }

  public getClient(): PrismaClient {
    return this.prisma;
  }

  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      console.log('✅ Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      console.log('✅ Database disconnected successfully');
    } catch (error) {
      console.error('❌ Database disconnection failed:', error);
      throw error;
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('❌ Database health check failed:', error);
      return false;
    }
  }

  public async runMigrations(): Promise<void> {
    try {
      // Note: In production, migrations should be run separately
      // This is for development convenience
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 Running database migrations...');
        // Migrations are typically run via CLI: bunx prisma migrate dev
        console.log('ℹ️  Run "bunx prisma migrate dev" to apply migrations');
      }
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }
}
