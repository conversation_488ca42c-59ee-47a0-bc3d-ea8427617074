import type { ApplicationEnvironment } from '@beauty-crm/platform-environment-names';
import { PrismaClient } from '@prisma/client';
import { PrismaPg } from '@prisma/adapter-pg';
import pg from 'pg';
import type { Salon } from '../types';

// Use standard salon service database URL - all salons share the same database with table-level isolation
// In Prisma v7, datasource URL is configured in prisma.config.ts and adapter is required
const connectionString =
  process.env.DATABASE_URL ||
  '********************************************************************/beauty_crm_salon';
const pool = new pg.Pool({ connectionString });
const adapter = new PrismaPg(pool);
const prisma = new PrismaClient({ adapter });

export const getSalon = async (
  _env: ApplicationEnvironment,
  salonId: string,
): Promise<Salon> => {
  const salon = await prisma.salon.findUnique({
    where: { id: salonId },
  });

  if (!salon) {
    throw new Error('Salon not found');
  }

  return salon;
};

export const getSalons = async (
  _env: ApplicationEnvironment,
): Promise<Salon[]> => {
  return prisma.salon.findMany({
    orderBy: [{ name: 'asc' }, { locationName: 'asc' }],
    where: {
      isActive: true,
    },
  });
};
