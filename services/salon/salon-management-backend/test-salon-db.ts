#!/usr/bin/env bun

/**
 * Integration test to verify salon creation works with the database
 * This script:
 * 1. Creates a salon via the API
 * 2. Verifies it exists in the database
 * 3. Retrieves it via the API
 * 4. Cleans up the test data
 */

import { PrismaClient } from '@prisma/client';
import { writeFileSync } from 'node:fs';

const logFile = '/tmp/salon-test-results.txt';
const logs: string[] = [];

function log(message: string) {
  console.log(message);
  logs.push(message);
  writeFileSync(logFile, logs.join('\n'));
}

const API_URL = 'http://localhost:4000';
const prisma = new PrismaClient({
  datasourceUrl:
    process.env.DATABASE_URL ||
    'postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_salon',
});

interface SalonResponse {
  success: boolean;
  data: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    locationName: string;
    timezone: string;
    businessHoursStart: string;
    businessHoursEnd: string;
    isActive: boolean;
  };
}

async function testSalonCreation() {
  log('🧪 Testing Salon Creation with Database Integration\n');

  const timestamp = Date.now();
  const testSalon = {
    businessHoursEnd: '17:00',
    businessHoursStart: '09:00',
    email: `test-${timestamp}@example.com`,
    locationName: `Test Location ${timestamp}`,
    name: `Test Salon ${timestamp}`,
    phone: '+1234567890',
    timezone: 'America/New_York',
  };

  let salonId: string | null = null;

  try {
    // Step 1: Create salon via API
    console.log('📝 Step 1: Creating salon via API...');
    console.log(`   Name: ${testSalon.name}`);
    console.log(`   Email: ${testSalon.email}\n`);

    const createResponse = await fetch(`${API_URL}/salons`, {
      body: JSON.stringify(testSalon),
      headers: { 'Content-Type': 'application/json' },
      method: 'POST',
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      throw new Error(
        `API request failed: ${createResponse.status} - ${errorText}`,
      );
    }

    const createData: SalonResponse = await createResponse.json();
    console.log('✅ API Response:', JSON.stringify(createData, null, 2), '\n');

    if (!createData.success || !createData.data?.id) {
      throw new Error('Failed to create salon - no ID returned');
    }

    salonId = createData.data.id;
    console.log(`✅ Salon created with ID: ${salonId}\n`);

    // Step 2: Verify salon exists in database
    console.log('🔍 Step 2: Verifying salon exists in database...');
    const dbSalon = await prisma.salon.findUnique({
      where: { id: salonId },
    });

    if (!dbSalon) {
      throw new Error('Salon not found in database!');
    }

    console.log('✅ Database Record:', JSON.stringify(dbSalon, null, 2), '\n');

    // Step 3: Verify fields match
    console.log('🔎 Step 3: Verifying salon data matches...');
    const checks = [
      { actual: dbSalon.name, expected: testSalon.name, field: 'name' },
      { actual: dbSalon.email, expected: testSalon.email, field: 'email' },
      {
        actual: dbSalon.locationName,
        expected: testSalon.locationName,
        field: 'locationName',
      },
      {
        actual: dbSalon.timezone,
        expected: testSalon.timezone,
        field: 'timezone',
      },
      {
        actual: dbSalon.businessHoursStart,
        expected: testSalon.businessHoursStart,
        field: 'businessHoursStart',
      },
      {
        actual: dbSalon.businessHoursEnd,
        expected: testSalon.businessHoursEnd,
        field: 'businessHoursEnd',
      },
      { actual: dbSalon.isActive, expected: true, field: 'isActive' },
    ];

    for (const check of checks) {
      if (check.actual !== check.expected) {
        throw new Error(
          `${check.field} mismatch! Expected: ${check.expected}, Got: ${check.actual}`,
        );
      }
      console.log(`   ✓ ${check.field}: ${check.actual}`);
    }

    console.log('\n✅ All fields verified successfully!\n');

    // Step 4: Retrieve via API
    console.log('📥 Step 4: Retrieving salon via API...');
    const getResponse = await fetch(`${API_URL}/salons/${salonId}`);
    const getData: SalonResponse = await getResponse.json();

    if (!getData.success) {
      throw new Error('Failed to retrieve salon via API');
    }

    console.log('✅ GET Response:', JSON.stringify(getData, null, 2), '\n');

    console.log('✅ TEST PASSED!\n');
    console.log('Summary:');
    console.log('  ✓ Salon created via API');
    console.log('  ✓ Salon exists in database');
    console.log('  ✓ All fields match expected values');
    console.log('  ✓ Salon retrievable via API\n');
  } catch (error) {
    console.error('❌ TEST FAILED!');
    console.error(error);
    process.exit(1);
  } finally {
    // Cleanup
    if (salonId) {
      console.log('🧹 Cleaning up test data...');
      await prisma.salon.delete({ where: { id: salonId } });
      console.log('✅ Test data cleaned up\n');
    }

    await prisma.$disconnect();
  }
}

testSalonCreation();
