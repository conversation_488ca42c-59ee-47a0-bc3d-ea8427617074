{"browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "dependencies": {"@beauty-crm/platform-introvertic-ui": "^1.0.0", "@tanstack/react-query": "^5.90.16", "axios": "^1.13.2", "clsx": "^2.1.1", "lucide-react": "^0.562.0", "react": "^19.2.3", "react-dom": "^19.2.3", "react-router-dom": "^7.12.0", "tailwind-merge": "^3.4.0", "zod": "^4.3.5"}, "description": "Salon management frontend service for Beauty CRM", "devDependencies": {"@tailwindcss/vite": "^4.1.18", "@types/node": "^25.0.3", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@vitejs/plugin-react": "^5.1.2", "esbuild": "^0.27.2", "rimraf": "^6.1.2", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vite": "^7.3.1", "vitest": "^4.0.6"}, "name": "@beauty-crm/salon-management-frontend", "overrides": {"esbuild": "^0.25.5"}, "private": true, "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"build": "npx vite build", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "vite --host 0.0.0.0 --port 5173", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "start": "vite preview --host 0.0.0.0 --port 5173", "start:dev": "vite --host 0.0.0.0 --port 5173", "start:prod": "vite preview --host 0.0.0.0 --port 5173", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "1.0.0"}