{"description": "Salon management services for Beauty CRM", "devDependencies": {"bun": "^1.3.5"}, "name": "@beauty-crm/salon-services", "private": true, "scripts": {"build": "docker compose -f docker-compose.app.yml build", "logs": "docker compose -f docker-compose.app.yml logs -f", "start": "docker compose -f docker-compose.app.yml up -d", "stop": "docker compose -f docker-compose.app.yml down"}, "version": "1.0.0", "workspaces": ["salon-management-backend", "salon-management-frontend"]}