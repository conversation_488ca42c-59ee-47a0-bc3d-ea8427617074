// Staff Domain Schema
// Comprehensive staff catalog management

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
}

// Core Staff Model
model Staff {
  id        String   @id @default(cuid())
  salonId   String // Multi-tenant support
  firstName String
  lastName  String
  notes     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Rich content
  media StaffMedia[]

  // Categorization
  categoryId String?
  category   StaffCategory? @relation(fields: [categoryId], references: [id])
  tags       StaffTag[]

  // Business rules
  workHours    WorkHour[]
  availability StaffAvailability[]
  restrictions StaffRestriction[]
  addOns       StaffAddOn[]

  // Prerequisites and relationships
  prerequisites StaffPrerequisite[] @relation("StaffPrerequisites")
  dependents    StaffPrerequisite[] @relation("PrerequisiteStaffs")

  // Analytics
  analytics StaffAnalytics?

  @@index([salonId])
  @@index([categoryId])
  @@index([isActive])
}

// Staff Categories (hierarchical)
model StaffCategory {
  id          String          @id @default(cuid())
  name        String
  description String?
  parentId    String?
  parent      StaffCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    StaffCategory[] @relation("CategoryHierarchy")
  icon        String? // Icon identifier
  color       String? // Brand color
  sortOrder   Int             @default(0)
  isActive    Boolean         @default(true)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  staffs Staff[]

  @@unique([name, parentId])
}

// Staff Media (images, videos, icons)
model StaffMedia {
  id      String @id @default(cuid())
  staffId String
  staff   Staff  @relation(fields: [staffId], references: [id], onDelete: Cascade)

  type      MediaType
  url       String
  altText   String?
  caption   String?
  sortOrder Int       @default(0)
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())

  @@index([staffId])
}

enum MediaType {
  IMAGE
  VIDEO
  ICON
  THUMBNAIL
}

// Staff Tags (flexible labeling)
model StaffTag {
  id          String   @id @default(cuid())
  name        String   @unique
  color       String?
  description String?
  createdAt   DateTime @default(now())

  staffs Staff[]
}

// Staff Work Hours (migrated from appointment service)
model WorkHour {
  id        String    @id @default(cuid())
  salonId   String // Salon ID for multi-tenancy
  staffId   String
  staff     Staff     @relation(fields: [staffId], references: [id], onDelete: Cascade)
  dayOfWeek DayOfWeek
  startTime DateTime
  endTime   DateTime
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@index([staffId])
  @@index([salonId])
  @@index([dayOfWeek])
}

// Staff Availability Rules (flexible availability patterns)
model StaffAvailability {
  id      String @id @default(cuid())
  staffId String
  staff   Staff  @relation(fields: [staffId], references: [id], onDelete: Cascade)

  dayOfWeek String // JSON array of available days
  timeFrom  String // HH:MM format
  timeTo    String // HH:MM format

  validFrom DateTime?
  validTo   DateTime?

  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  @@index([staffId])
}

// Staff Restrictions
model StaffRestriction {
  id      String @id @default(cuid())
  staffId String
  staff   Staff  @relation(fields: [staffId], references: [id], onDelete: Cascade)

  type        RestrictionType
  description String
  value       String? // Additional data (age limit, etc.)

  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  @@index([staffId])
}

enum RestrictionType {
  AGE_MINIMUM
  AGE_MAXIMUM
  GENDER_SPECIFIC
  HEALTH_CONDITION
  PREGNANCY_RESTRICTION
  SKIN_TYPE
  CUSTOM
}

// Staff Add-ons
model StaffAddOn {
  id      String @id @default(cuid())
  staffId String
  staff   Staff  @relation(fields: [staffId], references: [id], onDelete: Cascade)

  name        String
  description String?
  price       Decimal
  duration    Int // Additional duration in minutes
  isOptional  Boolean @default(true)

  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([staffId])
}

// Staff Prerequisites (staff dependencies)
model StaffPrerequisite {
  id                  String @id @default(cuid())
  staffId             String
  prerequisiteStaffId String

  staff             Staff @relation("StaffPrerequisites", fields: [staffId], references: [id], onDelete: Cascade)
  prerequisiteStaff Staff @relation("PrerequisiteStaffs", fields: [prerequisiteStaffId], references: [id], onDelete: Cascade)

  description String?
  isRequired  Boolean @default(true)
  daysBetween Int? // Minimum days between staffs

  createdAt DateTime @default(now())

  @@unique([staffId, prerequisiteStaffId])
}

// Staff Analytics
model StaffAnalytics {
  id      String @id @default(cuid())
  staffId String @unique
  staff   Staff  @relation(fields: [staffId], references: [id], onDelete: Cascade)

  totalBookings   Int     @default(0)
  totalRevenue    Decimal @default(0)
  averageRating   Decimal @default(0)
  popularityScore Decimal @default(0)

  lastBookedAt DateTime?
  updatedAt    DateTime  @updatedAt
}

// Enums
enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}
