import type { Context } from 'hono';
import { z } from 'zod';
import type { StaffService } from '../application/services/StaffService';

// Validation schemas
const createStaffSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  notes: z.string().optional(),
  salonId: z.string().min(1, 'Salon ID is required'),
});

const updateStaffSchema = createStaffSchema.partial().omit({ salonId: true });

const searchFiltersSchema = z.object({
  isActive: z.coerce.boolean().optional(),
  maxDuration: z.coerce.number().optional(),
  maxPrice: z.coerce.number().optional(),
  minDuration: z.coerce.number().optional(),
  minPrice: z.coerce.number().optional(),
  query: z.string().optional(),
});

const bulkToggleActiveSchema = z.object({
  isActive: z.boolean(),
  staffIds: z.array(z.string()).min(1, 'At least one staff ID is required'),
});

export class StaffController {
  constructor(private staffService: StaffService) {}

  // GET /api/staff?salonId=salon1
  getAll = async (c: Context) => {
    try {
      const { salonId } = c.req.query();

      if (!salonId) {
        return c.json({ error: 'Salon ID is required', success: false }, 400);
      }

      const staff = await this.staffService.getAllStaff(salonId);

      return c.json({
        count: staff.length,
        data: staff.map((t) => t.toJSON()),
        success: true,
      });
    } catch (error) {
      console.error('Error getting staff:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // GET /api/staff/active?salonId=salon1
  getActive = async (c: Context) => {
    try {
      const { salonId } = c.req.query();

      if (!salonId) {
        return c.json({ error: 'Salon ID is required', success: false }, 400);
      }

      const staff = await this.staffService.getActiveStaff(salonId);

      return c.json({
        count: staff.length,
        data: staff.map((t) => t.toJSON()),
        success: true,
      });
    } catch (error) {
      console.error('Error getting active staff:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // GET /api/staff/:id
  getById = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json({ error: 'Staff ID is required', success: false }, 400);
      }

      const staff = await this.staffService.getStaffById(id);

      if (!staff) {
        return c.json({ error: 'Staff not found', success: false }, 404);
      }

      return c.json({ data: staff.toJSON(), success: true });
    } catch (error) {
      console.error('Error getting staff by ID:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // GET /api/staff/search?salonId=salon1&query=facial
  search = async (c: Context) => {
    try {
      const { salonId, ...filters } = c.req.query();

      if (!salonId) {
        return c.json({ error: 'Salon ID is required', success: false }, 400);
      }

      const validationResult = searchFiltersSchema.safeParse(filters);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid search filters',
            success: false,
          },
          400,
        );
      }

      const staff = await this.staffService.searchStaff(
        salonId,
        validationResult.data,
      );

      return c.json({
        count: staff.length,
        data: staff.map((t) => t.toJSON()),
        filters: validationResult.data,
        success: true,
      });
    } catch (error) {
      console.error('Error searching staff:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // POST /api/staffs
  create = async (c: Context) => {
    try {
      const body = await c.req.json();

      const validationResult = createStaffSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid staff data',
            success: false,
          },
          400,
        );
      }

      const staff = await this.staffService.createStaff(validationResult.data);

      return c.json(
        {
          data: staff.toJSON(),
          message: 'Staff created successfully',
          success: true,
        },
        201,
      );
    } catch (error) {
      console.error('Error creating staff:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // PUT /api/staffs/:id
  update = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json({ error: 'Staff ID is required', success: false }, 400);
      }

      const body = await c.req.json();

      const validationResult = updateStaffSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid staff data',
            success: false,
          },
          400,
        );
      }

      const staff = await this.staffService.updateStaff(
        id,
        validationResult.data,
      );

      return c.json({
        data: staff.toJSON(),
        message: 'Staff updated successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error updating staff:', error);

      if (error instanceof Error && error.message === 'Staff not found') {
        return c.json({ error: error.message, success: false }, 404);
      }

      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // PATCH /api/staffs/:id/activate
  activate = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json({ error: 'Staff ID is required', success: false }, 400);
      }

      const staff = await this.staffService.activateStaff(id);

      return c.json({
        data: staff.toJSON(),
        message: 'Staff activated successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error activating staff:', error);

      if (error instanceof Error && error.message === 'Staff not found') {
        return c.json({ error: error.message, success: false }, 404);
      }

      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // PATCH /api/staffs/:id/deactivate
  deactivate = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json({ error: 'Staff ID is required', success: false }, 400);
      }

      const staff = await this.staffService.deactivateStaff(id);

      return c.json({
        data: staff.toJSON(),
        message: 'Staff deactivated successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error deactivating staff:', error);

      if (error instanceof Error && error.message === 'Staff not found') {
        return c.json({ error: error.message, success: false }, 404);
      }

      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // DELETE /api/staff/:id
  delete = async (c: Context) => {
    try {
      const { id } = c.req.param();

      if (!id) {
        return c.json({ error: 'Staff ID is required', success: false }, 400);
      }

      await this.staffService.deleteStaff(id);

      return c.json({
        message: 'Staff deleted successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error deleting staff:', error);

      if (error instanceof Error && error.message === 'Staff not found') {
        return c.json({ error: error.message, success: false }, 404);
      }

      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  // POST /api/staff/bulk/toggle-active
  bulkToggleActive = async (c: Context) => {
    try {
      const { salonId } = c.req.query();
      const body = await c.req.json();

      if (!salonId) {
        return c.json({ error: 'Salon ID is required', success: false }, 400);
      }

      const validationResult = bulkToggleActiveSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.issues,
            error: 'Invalid bulk toggle data',
            success: false,
          },
          400,
        );
      }

      const updatedCount = await this.staffService.bulkToggleActive(
        salonId,
        validationResult.data.staffIds,
        validationResult.data.isActive,
      );

      return c.json({
        data: { updatedCount },
        message: `${validationResult.data.isActive ? 'Activated' : 'Deactivated'} ${updatedCount} staffs`,
        success: true,
      });
    } catch (error) {
      console.error('Error bulk toggling active status:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };
}
