{"dependencies": {"@beauty-crm/platform-computing-lifecycle": "^1.0.0", "@beauty-crm/platform-db-client": "^1.0.4", "@beauty-crm/platform-eventing": "^1.0.0", "@hono/node-server": "^1.19.7", "@paralleldrive/cuid2": "^3.0.6", "@prisma/client": "^7.2.0", "axios": "^1.13.2", "hono": "^4.11.3", "nats": "^2.29.3", "uuid": "^13.0.0", "zod": "^4.3.5"}, "description": "API service managing staff operations, business rules, and staff workflows for Beauty CRM", "devDependencies": {"@types/bun": "latest", "@types/node": "^25.0.3", "@types/uuid": "^11.0.0", "esbuild": "^0.27.2", "prisma": "^7.2.0", "tsx": "^4.21.0", "typescript": "^5.9.3", "vitest": "^4.0.6"}, "main": "src/index.ts", "module": "index.ts", "name": "@beauty-crm/staff-management-backend", "peerDependencies": {"typescript": "^5"}, "private": true, "scripts": {"build": "bun build src/index.ts --outdir dist --target bun", "db:generate": "bunx prisma generate", "db:migrate": "bunx prisma migrate dev", "db:push": "bunx prisma db push", "db:studio": "bunx prisma studio", "dev": "bun run --watch src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "start": "bun run src/index.ts"}, "type": "module", "version": "1.0.0"}