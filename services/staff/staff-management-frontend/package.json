{"dependencies": {"@beauty-crm/platform-introvertic-ui": "^1.0.0", "@tanstack/react-query": "^5.90.16", "axios": "^1.13.2", "clsx": "^2.1.1", "lucide-react": "^0.562.0", "react": "^19.2.3", "react-dom": "^19.2.3", "react-router-dom": "^7.12.0", "tailwind-merge": "^3.4.0", "zod": "^4.3.5"}, "description": "Staff management microfrontend for Beauty CRM", "devDependencies": {"@biomejs/biome": "^2.3.11", "@tailwindcss/vite": "^4.1.18", "@types/node": "^25.0.3", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@vitejs/plugin-react": "^5.1.2", "esbuild": "^0.27.2", "tailwindcss": "^4.1.16", "typescript": "^5.9.3", "vite": "^7.3.1", "vitest": "^4.0.6"}, "name": "@beauty-crm/staff-management-frontend", "overrides": {"esbuild": "^0.25.5"}, "private": true, "scripts": {"build": "tsc && vite build", "dev": "vite", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "vite preview"}, "type": "module", "version": "0.0.0"}