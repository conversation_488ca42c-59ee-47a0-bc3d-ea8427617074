import { type Response, Router } from 'express';
import { z } from 'zod';
import type { SalonRequest } from '../middleware/salonMiddleware';
import { SalonService } from '../services/SalonService';
import { logger } from '../utils/logger';

const router = Router();

// Validation schemas
const salonSettingsSchema = z.object({
  branding: z
    .object({
      logo: z.string().url().optional(),
      name: z.string().optional(),
      primaryColor: z.string().optional(),
    })
    .optional(),
  features: z.record(z.boolean()).optional(),
  notifications: z
    .object({
      email: z.boolean().optional(),
      push: z.boolean().optional(),
      sms: z.boolean().optional(),
    })
    .optional(),
});

/**
 * Get current salon information
 */
router.get('/current', async (req: SalonRequest, res: Response) => {
  try {
    if (!req.salon) {
      return res.status(400).json({
        error: 'No salon context available',
        success: false,
      });
    }

    res.json({
      data: {
        id: req.salon.id,
        name: req.salon.name,
        settings: req.salon.settings,
        slug: req.salon.slug,
        status: req.salon.status,
      },
      success: true,
    });
  } catch (error) {
    logger.error('Error getting current salon:', error);
    res.status(500).json({
      error: 'Failed to get salon information',
      success: false,
    });
  }
});

/**
 * Get salon settings
 */
router.get('/settings', async (req: SalonRequest, res: Response) => {
  try {
    if (!req.salon) {
      return res.status(400).json({
        error: 'No salon context available',
        success: false,
      });
    }

    const settings = await SalonService.getSalonSettings(req.salon.id);

    res.json({
      data: settings,
      success: true,
    });
  } catch (error) {
    logger.error('Error getting salon settings:', error);
    res.status(500).json({
      error: 'Failed to get salon settings',
      success: false,
    });
  }
});

/**
 * Update salon settings
 */
router.put('/settings', async (req: SalonRequest, res: Response) => {
  try {
    if (!req.salon) {
      return res.status(400).json({
        error: 'No salon context available',
        success: false,
      });
    }

    // Validate request body
    const validation = salonSettingsSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        details: validation.error.issues,
        error: 'Invalid settings format',
        success: false,
      });
    }

    // Check if user has admin role
    if (req.user?.role !== 'admin' && req.user?.role !== 'owner') {
      return res.status(403).json({
        error: 'Insufficient permissions to update settings',
        success: false,
      });
    }

    const success = await SalonService.updateSalonSettings(
      req.salon.id,
      validation.data,
    );

    if (!success) {
      return res.status(500).json({
        error: 'Failed to update salon settings',
        success: false,
      });
    }

    res.json({
      message: 'Settings updated successfully',
      success: true,
    });
  } catch (error) {
    logger.error('Error updating salon settings:', error);
    res.status(500).json({
      error: 'Failed to update salon settings',
      success: false,
    });
  }
});

/**
 * Check if salon has specific feature
 */
router.get('/features/:feature', async (req: SalonRequest, res: Response) => {
  try {
    if (!req.salon) {
      return res.status(400).json({
        error: 'No salon context available',
        success: false,
      });
    }

    const { feature } = req.params;
    const hasFeature = await SalonService.hasFeature(req.salon.id, feature);

    res.json({
      data: {
        enabled: hasFeature,
        feature,
      },
      success: true,
    });
  } catch (error) {
    logger.error('Error checking salon feature:', error);
    res.status(500).json({
      error: 'Failed to check feature availability',
      success: false,
    });
  }
});

/**
 * Public endpoint to get salon by slug (for subdomain resolution)
 */
router.get(
  '/public/by-slug/:slug',
  async (req: SalonRequest, res: Response) => {
    try {
      const { slug } = req.params;
      const salon = await SalonService.getBySlug(slug);

      if (!salon) {
        return res.status(404).json({
          error: 'Salon not found',
          success: false,
        });
      }

      // Return minimal public information
      res.json({
        data: {
          id: salon.id,
          name: salon.name,
          slug: salon.slug,
          status: salon.status,
        },
        success: true,
      });
    } catch (error) {
      logger.error('Error getting salon by slug:', error);
      res.status(500).json({
        error: 'Failed to get salon information',
        success: false,
      });
    }
  },
);

export { router as salonRoutes };
