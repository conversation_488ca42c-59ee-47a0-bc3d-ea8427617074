import type { NextFunction, Request, Response } from 'express';
import { z } from 'zod';
import { SalonService } from '../services/SalonService';
import { logger } from '../utils/logger';

// CUID validation regex
const CUID_REGEX = /^c[a-z0-9]{24}$/;

// Salon validation schema
const salonSchema = z.object({
  salonId: z.string().regex(CUID_REGEX, 'Salon ID must be a valid CUID format'),
});

// Extended Request interface to include salon context
export interface SalonRequest extends Request {
  // Add salon context to request
  salon?: {
    id: string;
    slug: string;
    name: string;
    status: 'active' | 'suspended' | 'inactive';
    settings: Record<string, unknown>;
  };
  user?: {
    id: string;
    role: string;
    salonId: string;
  };
}

/**
 * Multi-salon middleware that extracts and validates salon ID from requests
 * Supports multiple ways to pass salon context:
 * 1. Header: x-salon-id
 * 2. Query parameter: salonId
 * 3. Subdomain: {salon-slug}.beauty-crm.com
 * 4. Path parameter: /api/salons/{salonId}/...
 */
export const salonMiddleware = async (
  req: SalonRequest,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    let salonId: string | undefined;
    let salonSlug: string | undefined;

    // Method 1: Extract from header
    salonId = req.headers['x-salon-id'] as string;

    // Method 2: Extract from query parameter
    if (!salonId) {
      salonId = req.query.salonId as string;
    }

    // Method 3: Extract from subdomain
    if (!salonId) {
      const host = req.headers.host || '';
      const subdomain = host.split('.')[0];

      // Check if it's a salon subdomain (not www, api, etc.)
      if (subdomain && !['www', 'api', 'admin', 'app'].includes(subdomain)) {
        salonSlug = subdomain;
      }
    }

    // Method 4: Extract from path parameter
    if (!salonId) {
      const pathMatch = req.path.match(/^\/api\/salons\/([a-z0-9]+)/);
      if (pathMatch) {
        salonId = pathMatch[1];
      }
    }

    // If we have a slug but no ID, resolve it
    if (salonSlug && !salonId) {
      const salon = await SalonService.getBySlug(salonSlug);
      if (salon) {
        salonId = salon.id;
      }
    }

    // Validate salon ID format if provided
    if (salonId) {
      const validation = salonSchema.safeParse({ salonId });
      if (!validation.success) {
        res.status(400).json({
          details: validation.error.issues,
          error: 'Invalid salon ID format',
          success: false,
        });
        return;
      }

      // Get salon information
      const salon = await SalonService.getById(salonId);
      if (!salon) {
        res.status(404).json({
          error: 'Salon not found',
          success: false,
        });
        return;
      }

      // Check salon status
      if (salon.status !== 'active') {
        res.status(403).json({
          code: 'SALON_INACTIVE',
          error: `Salon is ${salon.status}`,
          success: false,
        });
        return;
      }

      // Add salon context to request
      req.salon = salon;

      logger.info('Salon context set', {
        method: req.method,
        path: req.path,
        salonId: salon.id,
        salonSlug: salon.slug,
      });
    } else {
      // For some routes, salon context might be optional
      // Check if this is a salon-required route
      const salonRequiredPaths = [
        '/api/appointments',
        '/api/staff',
        '/api/treatments',
      ];

      const requiresSalon = salonRequiredPaths.some((path) =>
        req.path.startsWith(path),
      );

      if (requiresSalon) {
        res.status(400).json({
          error: 'Salon ID is required for this operation',
          hint: 'Provide salon ID via header (x-salon-id), query parameter (salonId), or subdomain',
          success: false,
        });
        return;
      }
    }

    next();
  } catch (error) {
    logger.error('Salon middleware error:', error);
    res.status(500).json({
      error: 'Internal server error during salon validation',
      success: false,
    });
  }
};
