{"author": "Beauty CRM Platform Team", "dependencies": {"@beauty-crm/platform-db-client": "^1.0.0", "@beauty-crm/platform-logger": "^1.0.0", "@beauty-crm/product-domain-types": "^1.0.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "helmet": "^7.2.0", "http-proxy-middleware": "^2.0.9", "jsonwebtoken": "^9.0.2", "nats": "^2.29.3", "redis": "^4.7.1", "winston": "^3.18.3", "zod": "^3.25.76"}, "description": "Multi-tenant API Gateway for Beauty CRM SaaS", "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^4.17.25", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.24", "@types/winston": "^2.4.4", "jest": "^29.7.0", "tsx": "^4.20.6", "typescript": "^5.9.3"}, "keywords": ["api-gateway", "multi-tenant", "saas", "beauty-crm", "microservices"], "license": "MIT", "main": "dist/index.js", "name": "@beauty-crm/api-gateway", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "start": "node dist/index.js", "test": "jest"}, "version": "1.0.0"}